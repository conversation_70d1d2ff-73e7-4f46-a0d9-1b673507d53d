name: Lint

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  run-linters:
    name: Run linters
    runs-on: ubuntu-20.04

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: 3.9
          cache: 'pip'
          cache-dependency-path: '**/requirements*.txt'
      - name: Install Python (development) dependencies
        run: |
          pip install -r requirements-dev.txt
      - name: Run flake8
        run: |
          flake8
      - name: Run black
        if: always()
        run: |
          black --check dinov2
      - name: Run pylint
        if: always()
        run: |
          pylint --exit-zero dinov2
