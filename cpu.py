import cpu
import time
import psutil  # 添加psutil库来监控系统资源

# 初始化wandb项目
cpu.init(project="cpu_monitoring")

while True:
    # 获取CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    # 获取内存使用情况
    memory = psutil.virtual_memory()
    
    # 记录多个指标
    cpu.log({
        "cpu_usage": cpu_percent,
        "memory_usage": memory.percent,
        "memory_available": memory.available / (1024 * 1024 * 1024),  # 转换为GB
    })
    
    time.sleep(1)

