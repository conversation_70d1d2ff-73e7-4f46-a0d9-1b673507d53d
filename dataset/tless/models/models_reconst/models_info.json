{"1": {"diameter": 63.5151, "min_x": -17.4958, "min_y": -17.4958, "min_z": -30.6, "size_x": 34.9916, "size_y": 34.9916, "size_z": 61.2, "symmetries_continuous": [{"axis": [0, 0, 1], "offset": [0, 0, 0]}]}, "2": {"diameter": 66.1512, "min_x": -21.6448, "min_y": -21.6448, "min_z": -30.8511, "size_x": 43.2896, "size_y": 43.2896, "size_z": 61.7022, "symmetries_continuous": [{"axis": [0, 0, 1], "offset": [0, 0, 0]}]}, "3": {"diameter": 65.3491, "min_x": -23.8837, "min_y": -23.8842, "min_z": -30.8351, "size_x": 47.7627, "size_y": 47.7616, "size_z": 61.6702, "symmetries_continuous": [{"axis": [0, 0, 1], "offset": [0, 0, 0]}]}, "4": {"diameter": 80.7257, "min_x": -19.9978, "min_y": -19.9929, "min_z": -39, "size_x": 39.9956, "size_y": 39.9858, "size_z": 78, "symmetries_continuous": [{"axis": [0, 0, 1], "offset": [0, 0, 0]}]}, "5": {"diameter": 108.69, "min_x": -47.5, "min_y": -26.75, "min_z": -29.5, "size_x": 95, "size_y": 53.5, "size_z": 59, "symmetries_discrete": [[-1, -4.63077e-14, 0, 0, 4.63077e-14, -1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]]}, "6": {"diameter": 108.265, "min_x": -44.7, "min_y": -25, "min_z": -27.75, "size_x": 89.4, "size_y": 50, "size_z": 55.5, "symmetries_discrete": [[-1, -4.63077e-14, 0, 0, 4.63077e-14, -1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]]}, "7": {"diameter": 178.615, "min_x": -75, "min_y": -44.7, "min_z": -30.75, "size_x": 150, "size_y": 89.4, "size_z": 61.5, "symmetries_discrete": [[-1, -4.63077e-14, 0, 0, 4.63077e-14, -1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]]}, "8": {"diameter": 217.156, "min_x": -93.0393, "min_y": -52.6672, "min_z": -30.0073, "size_x": 186.079, "size_y": 105.334, "size_z": 60.0146, "symmetries_discrete": [[-1, 2.70407e-06, -5.75524e-05, -0.0266153, -2.68381e-06, -1, -0.000352012, -0.0926988, -5.75534e-05, -0.000352012, 1, -5.29696e-05, 0, 0, 0, 1]]}, "9": {"diameter": 144.546, "min_x": -60.625, "min_y": -39.25, "min_z": -31.349, "size_x": 121.25, "size_y": 78.5, "size_z": 62.698, "symmetries_discrete": [[-1, -4.63077e-14, 0, 0, 4.63077e-14, -1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]]}, "10": {"diameter": 90.2112, "min_x": -40.3299, "min_y": -21.0036, "min_z": -31.7513, "size_x": 80.6598, "size_y": 42.0072, "size_z": 63.5026, "symmetries_discrete": [[-0.999937, 0.000191896, 0.011248, -0.143745, 0.000191922, -0.999418, 0.0341122, -0.517981, 0.011248, 0.0341122, 0.999355, 0.00964629, 0, 0, 0, 1]]}, "11": {"diameter": 76.5978, "min_x": -33.1456, "min_y": -24.121, "min_z": -27.65, "size_x": 66.2912, "size_y": 48.242, "size_z": 55.3, "symmetries_discrete": [[-1, 4.05208e-09, 1.93842e-08, 2.01636e-05, -4.05207e-09, -1, 1.83953e-07, -0.000163209, 1.93842e-08, 1.83953e-07, 1, 2.10486e-08, 0, 0, 0, 1]]}, "12": {"diameter": 86.0109, "min_x": -39.1656, "min_y": -28.8135, "min_z": -28.3, "size_x": 78.3312, "size_y": 57.627, "size_z": 56.6, "symmetries_discrete": [[-1, 2.42314e-09, -1.23147e-09, 0.00405229, -2.42314e-09, -1, 2.10363e-08, -0.000700235, -1.23147e-09, 2.10363e-08, 1, 4.85766e-08, 0, 0, 0, 1]]}, "13": {"diameter": 58.1257, "min_x": -19.9971, "min_y": -19.9943, "min_z": -23, "size_x": 39.9942, "size_y": 39.9886, "size_z": 46, "symmetries_continuous": [{"axis": [0, 0, 1], "offset": [0, 0, 0]}]}, "14": {"diameter": 71.9471, "min_x": -22.0673, "min_y": -22.0673, "min_z": -32.55, "size_x": 44.1346, "size_y": 44.1346, "size_z": 65.1, "symmetries_continuous": [{"axis": [0, 0, 1], "offset": [0, 0, 0]}]}, "15": {"diameter": 68.5692, "min_x": -22.2738, "min_y": -22.2771, "min_z": -27.5, "size_x": 44.5476, "size_y": 44.5542, "size_z": 55, "symmetries_continuous": [{"axis": [0, 0, 1], "offset": [0, 0, 0]}]}, "16": {"diameter": 69.1883, "min_x": -27.6338, "min_y": -27.6317, "min_z": -23.5, "size_x": 55.2676, "size_y": 55.2634, "size_z": 47, "symmetries_continuous": [{"axis": [0, 0, 1], "offset": [0, 0, 0]}]}, "17": {"diameter": 112.839, "min_x": -53.8871, "min_y": -53.8816, "min_z": -30.05, "size_x": 107.774, "size_y": 107.763, "size_z": 60.1, "symmetries_continuous": [{"axis": [0, 0, 1], "offset": [0, 0, 0]}]}, "18": {"diameter": 110.982, "min_x": -49.3605, "min_y": -49.3694, "min_z": -31.8869, "size_x": 98.721, "size_y": 98.7388, "size_z": 63.7738}, "19": {"diameter": 89.0689, "min_x": -32.75, "min_y": -38.25, "min_z": -23.5, "size_x": 65.5, "size_y": 76.5, "size_z": 47, "symmetries_discrete": [[-1, -3.47614e-14, 1.74419e-14, 0, -3.47614e-14, 1, -3.47614e-14, 0, -1.74419e-14, -3.47614e-14, -1, 0, 0, 0, 0, 1]]}, "20": {"diameter": 98.8887, "min_x": -41.5, "min_y": -37.75, "min_z": -23.5, "size_x": 83, "size_y": 75.5, "size_z": 47, "symmetries_discrete": [[-1, -3.47614e-14, 1.74419e-14, 0, -3.47614e-14, 1, -3.47614e-14, 0, -1.74419e-14, -3.47614e-14, -1, 0, 0, 0, 0, 1]]}, "21": {"diameter": 92.2527, "min_x": -38.45, "min_y": -39.4891, "min_z": -21.5, "size_x": 76.9, "size_y": 78.9782, "size_z": 43}, "22": {"diameter": 92.2527, "min_x": -38.45, "min_y": -39.4891, "min_z": -21.9935, "size_x": 76.9, "size_y": 78.9782, "size_z": 43.987}, "23": {"diameter": 142.587, "min_x": -68.9742, "min_y": -36.75, "min_z": -26.0326, "size_x": 137.948, "size_y": 73.5, "size_z": 52.0652, "symmetries_discrete": [[-1.0, 0.0, 1.2246467991473532e-16, 0.0, 0.0, 1.0, 0.0, 0.0, -1.2246467991473532e-16, 0.0, -1.0, 0.0, 0.0, 0.0, 0.0, 1.0]]}, "24": {"diameter": 84.736, "min_x": -21.4977, "min_y": -21.4985, "min_z": -40.3, "size_x": 42.9954, "size_y": 42.997, "size_z": 80.6, "symmetries_continuous": [{"axis": [0, 0, 1], "offset": [0, 0, 0]}]}, "25": {"diameter": 108.801, "min_x": -48, "min_y": -30.75, "min_z": -30.4006, "size_x": 96, "size_y": 61.5, "size_z": 60.8012, "symmetries_discrete": [[-1.0, -4.63077e-14, 0.0, -14.0, 4.63077e-14, -1.0, 0.0, 3.2415389999999997e-13, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]]}, "26": {"diameter": 108.801, "min_x": -48, "min_y": -30.75, "min_z": -30.3927, "size_x": 96, "size_y": 61.5, "size_z": 60.7854, "symmetries_discrete": [[-1.0, -4.63077e-14, 0.0, -14.0, 4.63077e-14, -1.0, 0.0, 3.2415389999999997e-13, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]]}, "27": {"diameter": 152.495, "min_x": -54.25, "min_y": -54.25, "min_z": -28, "size_x": 108.5, "size_y": 108.5, "size_z": 56, "symmetries_discrete": [[7.3887e-15, -1, 0, 0, 1, 7.3887e-15, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1], [-1, -6.05186e-14, 0, 0, 6.05186e-14, -1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1], [-2.86054e-14, 1, 0, 0, -1, -2.86054e-14, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]]}, "28": {"diameter": 124.778, "min_x": -49.75, "min_y": -49.75, "min_z": -24.2, "size_x": 99.5, "size_y": 99.5, "size_z": 48.4, "symmetries_discrete": [[-1, -4.63077e-14, 0, 0, 4.63077e-14, -1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]]}, "29": {"diameter": 134.227, "min_x": -56.5, "min_y": -39, "min_z": -28.4, "size_x": 113, "size_y": 78, "size_z": 56.8, "symmetries_discrete": [[-1, -4.63077e-14, 0, 0, 4.63077e-14, -1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]]}, "30": {"diameter": 88.7538, "min_x": -40, "min_y": -40, "min_z": -25.3, "size_x": 80, "size_y": 80, "size_z": 50.6, "symmetries_continuous": [{"axis": [0, 0, 1], "offset": [0, 0, 0]}]}}