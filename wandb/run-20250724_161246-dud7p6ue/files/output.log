特征提取器可训练参数数量: 1426564
偏移量预测器可训练参数数量: 961091
✅ 偏移量预测功能已启用
Hash编码器可训练参数数量: 135008
✅ Hash编码功能已启用
总可训练参数数量: 2522663
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [27:59<00:00,  2.70s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:34<00:00,  4.94it/s]6s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:44<00:00,  9.41it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:14<00:00,  5.22it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:59<00:00, 11.55it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  8.40it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:19<00:00, 16.46it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:04<00:00,  7.91it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [01:51<00:00, 21.39it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:50<00:00,  1.81s/it]

🔄 Epoch 1: 切换到Hash训练阶段
   🔒 冻结主要模块参数（model, offset_predictor）
   🔓 只训练Hash编码器参数
   📊 Hash优化器参数数量: 135008
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:24<00:00,  7.05it/s]3s/it]

Testing seen...
处理查询图像:   0%|                                                                                                                             | 0/981 [00:00<?, ?it/s]
  4%|█████                                                                                                                        | 1/25 [1:03:37<25:27:06, 3817.78s/it]
Traceback (most recent call last):
  File "train_new.py", line 382, in <module>
    main()
  File "train_new.py", line 308, in main
    testing_score = testing_utils_dino.test(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 219, in test
    feature_query = hash_encoder.encode_features(original_query_features)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/learnable_hash.py", line 712, in encode_features
    cls_hash = self.cls_hash_encoder(features['cls_feature'])
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/learnable_hash.py", line 303, in forward
    continuous_hash = self.encoder(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/container.py", line 139, in forward
    input = module(input)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/batchnorm.py", line 168, in forward
    return F.batch_norm(
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/functional.py", line 2436, in batch_norm
    _verify_batch_size(input.size())
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/functional.py", line 2404, in _verify_batch_size
    raise ValueError("Expected more than 1 value per channel when training, got input size {}".format(size))
ValueError: Expected more than 1 value per channel when training, got input size torch.Size([1, 64])
