{"time":"2025-07-24T19:33:25.140137201+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250724_193325-e3vvca6n/logs/debug-core.log"}
{"time":"2025-07-24T19:33:25.377171921+08:00","level":"INFO","msg":"created new stream","id":"e3vvca6n"}
{"time":"2025-07-24T19:33:25.377339271+08:00","level":"INFO","msg":"stream: started","id":"e3vvca6n"}
{"time":"2025-07-24T19:33:25.382138781+08:00","level":"INFO","msg":"sender: started","stream_id":"e3vvca6n"}
{"time":"2025-07-24T19:33:25.382206517+08:00","level":"INFO","msg":"handler: started","stream_id":"e3vvca6n"}
{"time":"2025-07-24T19:33:25.382227064+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"e3vvca6n"}
{"time":"2025-07-24T19:33:26.486694837+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-24T21:25:17.956261091+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/e3vvca6n/file_stream\": EOF"}
{"time":"2025-07-25T00:56:21.040530176+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/e3vvca6n/file_stream\": EOF"}
{"time":"2025-07-25T01:46:15.838683133+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-25T02:25:19.124238987+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/e3vvca6n/file_stream\": unexpected EOF"}
{"time":"2025-07-25T02:55:30.947382111+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-25T03:21:00.97371048+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-25T06:33:16.118939614+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-25T08:36:08.815481797+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/e3vvca6n/file_stream\": EOF"}
