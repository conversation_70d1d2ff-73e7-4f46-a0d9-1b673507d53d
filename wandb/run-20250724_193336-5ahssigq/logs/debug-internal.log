{"time":"2025-07-24T19:33:36.306148545+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250724_193336-5ahssigq/logs/debug-core.log"}
{"time":"2025-07-24T19:33:36.556063943+08:00","level":"INFO","msg":"created new stream","id":"5ahssigq"}
{"time":"2025-07-24T19:33:36.556148234+08:00","level":"INFO","msg":"handler: started","stream_id":"5ahssigq"}
{"time":"2025-07-24T19:33:36.556174207+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"5ahssigq"}
{"time":"2025-07-24T19:33:36.556282239+08:00","level":"INFO","msg":"sender: started","stream_id":"5ahssigq"}
{"time":"2025-07-24T19:33:36.556300053+08:00","level":"INFO","msg":"stream: started","id":"5ahssigq"}
{"time":"2025-07-24T19:33:37.194354423+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-24T22:02:01.381069013+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-24T22:56:09.180469672+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5ahssigq/file_stream\": EOF"}
{"time":"2025-07-24T22:56:54.178238955+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5ahssigq/file_stream\": EOF"}
{"time":"2025-07-25T00:56:19.075140574+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5ahssigq/file_stream\": dial tcp 198.18.0.6:443: connect: network is unreachable"}
{"time":"2025-07-25T00:56:20.326819866+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-07-25T01:36:54.176974324+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5ahssigq/file_stream\": EOF"}
{"time":"2025-07-25T01:40:57.779540362+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-25T02:23:20.534895511+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5ahssigq/file_stream\": EOF"}
{"time":"2025-07-25T03:21:02.918872993+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-25T08:35:28.90543856+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5ahssigq/file_stream\": EOF"}
{"time":"2025-07-25T08:35:43.905521116+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5ahssigq/file_stream\": EOF"}
{"time":"2025-07-25T08:40:43.794289249+08:00","level":"INFO","msg":"stream: closing","id":"5ahssigq"}
{"time":"2025-07-25T08:40:43.794323575+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-25T08:40:43.794967216+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-25T08:40:45.363444686+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-25T08:40:45.982767718+08:00","level":"INFO","msg":"handler: closed","stream_id":"5ahssigq"}
{"time":"2025-07-25T08:40:45.982824801+08:00","level":"INFO","msg":"sender: closed","stream_id":"5ahssigq"}
{"time":"2025-07-25T08:40:45.982817534+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"5ahssigq"}
{"time":"2025-07-25T08:40:45.98293339+08:00","level":"INFO","msg":"stream: closed","id":"5ahssigq"}
