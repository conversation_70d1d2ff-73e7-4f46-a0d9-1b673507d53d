{"time":"2025-07-24T19:34:30.863986431+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250724_193430-wpxls7jg/logs/debug-core.log"}
{"time":"2025-07-24T19:34:31.103815651+08:00","level":"INFO","msg":"created new stream","id":"wpxls7jg"}
{"time":"2025-07-24T19:34:31.103888681+08:00","level":"INFO","msg":"handler: started","stream_id":"wpxls7jg"}
{"time":"2025-07-24T19:34:31.103932568+08:00","level":"INFO","msg":"stream: started","id":"wpxls7jg"}
{"time":"2025-07-24T19:34:31.104056864+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"wpxls7jg"}
{"time":"2025-07-24T19:34:31.104175317+08:00","level":"INFO","msg":"sender: started","stream_id":"wpxls7jg"}
{"time":"2025-07-24T19:34:31.846710401+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-24T21:50:52.989891071+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/wpxls7jg/file_stream\": EOF"}
{"time":"2025-07-24T23:27:37.985914562+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/wpxls7jg/file_stream\": EOF"}
{"time":"2025-07-25T00:17:22.986606605+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/wpxls7jg/file_stream\": EOF"}
{"time":"2025-07-25T00:27:52.98593239+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/wpxls7jg/file_stream\": EOF"}
{"time":"2025-07-25T00:39:37.991896113+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/wpxls7jg/file_stream\": EOF"}
{"time":"2025-07-25T00:56:07.98467777+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/wpxls7jg/file_stream\": EOF"}
{"time":"2025-07-25T00:56:17.883445749+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/wpxls7jg/file_stream\": dial tcp 198.18.0.6:443: connect: network is unreachable"}
{"time":"2025-07-25T01:54:37.987323572+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/wpxls7jg/file_stream\": EOF"}
{"time":"2025-07-25T02:23:22.985217597+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/wpxls7jg/file_stream\": EOF"}
{"time":"2025-07-25T02:25:06.972440411+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-25T02:56:37.652157883+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-25T03:21:08.370304398+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-25T06:31:12.390850456+08:00","level":"INFO","msg":"stream: closing","id":"wpxls7jg"}
{"time":"2025-07-25T06:31:12.390884132+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-25T06:31:12.391702199+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-25T06:31:13.912108223+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-25T06:31:14.48291832+08:00","level":"INFO","msg":"handler: closed","stream_id":"wpxls7jg"}
{"time":"2025-07-25T06:31:14.482964595+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"wpxls7jg"}
{"time":"2025-07-25T06:31:14.483041127+08:00","level":"INFO","msg":"sender: closed","stream_id":"wpxls7jg"}
{"time":"2025-07-25T06:31:14.483052031+08:00","level":"INFO","msg":"stream: closed","id":"wpxls7jg"}
