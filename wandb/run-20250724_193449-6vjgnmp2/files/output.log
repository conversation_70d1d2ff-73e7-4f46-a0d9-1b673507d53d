特征提取器可训练参数数量: 1.43M
偏移量预测器可训练参数数量: 0.96M
✅ 偏移量预测功能已启用
Hash编码器可训练参数数量: 0.14M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 1开始
   📊 Hash阶段优化器参数数量: 0.14M
初始阶段优化器参数数量: 2.39M
100%|█████████████████████████████████████████████| 623/623 [55:39<00:00,  5.36s/it]
处理模板数据: 100%|███████████████████████████████| 170/170 [01:01<00:00,  2.76it/s]8s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████| 981/981 [06:26<00:00,  2.54it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  5.97it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████| 4848/4848 [17:45<00:00,  4.55it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:19<00:00,  3.87it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████| 4272/4272 [15:22<00:00,  4.63it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:11<00:00,  3.24it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████| 2377/2377 [10:14<00:00,  3.87it/s]
100%|█████████████████████████████████████████████| 623/623 [38:49<00:00,  3.74s/it]

🔄 Epoch 1: 切换到Hash训练阶段
   🔒 冻结主要模块参数（model, offset_predictor）
   🔓 只训练Hash编码器参数
   📊 Hash优化器参数数量: 0.14M
   📈 Hash训练学习率: 1e-05
   ✅ 优化器已切换到Hash训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:55<00:00,  3.04it/s]6s/it]

Testing seen...
处理查询图像:   0%|                                         | 0/981 [00:00<?, ?it/s]
  4%|█▋                                       | 1/25 [2:27:47<59:07:06, 8867.76s/it]
Traceback (most recent call last):
  File "train_new.py", line 404, in <module>
    main()
  File "train_new.py", line 330, in main
    testing_score = testing_utils_dino.test(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 219, in test
    feature_query = hash_encoder.encode_features(original_query_features)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/learnable_hash.py", line 712, in encode_features
    cls_hash = self.cls_hash_encoder(features['cls_feature'])
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/learnable_hash.py", line 303, in forward
    continuous_hash = self.encoder(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/container.py", line 139, in forward
    input = module(input)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/batchnorm.py", line 168, in forward
    return F.batch_norm(
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/functional.py", line 2436, in batch_norm
    _verify_batch_size(input.size())
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/functional.py", line 2404, in _verify_batch_size
    raise ValueError("Expected more than 1 value per channel when training, got input size {}".format(size))
ValueError: Expected more than 1 value per channel when training, got input size torch.Size([1, 64])
