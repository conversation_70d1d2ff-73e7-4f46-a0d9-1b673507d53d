特征提取器可训练参数数量: 1.43M
偏移量预测器可训练参数数量: 0.96M
✅ 偏移量预测功能已启用
Hash编码器可训练参数数量: 0.14M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 1开始
   📊 Hash阶段优化器参数数量: 0.14M
初始阶段优化器参数数量: 2.39M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:19<00:00,  2.82s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.78it/s]6s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:54<00:00, 17.86it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.13it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [03:21<00:00, 24.10it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:14<00:00,  5.29it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [02:54<00:00, 24.43it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.11it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [01:27<00:00, 27.20it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:05<00:00,  2.80s/it]

🔄 Epoch 1: 切换到Hash训练阶段
   🔒 冻结主要模块参数（model, offset_predictor）
   🔓 只训练Hash编码器参数
   📊 Hash优化器参数数量: 0.14M
   📈 Hash训练学习率: 1e-05
   ✅ 优化器已切换到Hash训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:28<00:00,  5.94it/s]4s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:57<00:00, 17.08it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.16it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [03:42<00:00, 21.79it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.29it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [03:14<00:00, 21.95it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.93it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:15<00:00, 17.55it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [28:32<00:00,  2.75s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:27<00:00,  6.28it/s]0s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:52<00:00, 18.51it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.14it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [03:40<00:00, 21.98it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.21it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [03:14<00:00, 21.97it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.58it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:22<00:00, 16.66it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [27:52<00:00,  2.68s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:27<00:00,  6.29it/s]4s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:52<00:00, 18.57it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.21it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [03:41<00:00, 21.93it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.25it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [03:46<00:00, 18.85it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.49it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:22<00:00, 16.64it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [27:17<00:00,  2.63s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:27<00:00,  6.29it/s]4s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:53<00:00, 18.46it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:14<00:00,  5.41it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [03:36<00:00, 22.43it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.39it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:15<00:00, 16.74it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.55it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:22<00:00, 16.63it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [26:46<00:00,  2.58s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:29<00:00,  5.69it/s]4s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:52<00:00, 18.51it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.14it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [03:34<00:00, 22.56it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.65it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:43<00:00, 15.05it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.55it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:22<00:00, 16.69it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [26:14<00:00,  2.53s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:26<00:00,  6.32it/s]4s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:53<00:00, 18.28it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.22it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [03:54<00:00, 20.66it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.60it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:45<00:00, 14.95it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.51it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:22<00:00, 16.67it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [25:41<00:00,  2.47s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:26<00:00,  6.31it/s]4s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:53<00:00, 18.47it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.11it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [04:19<00:00, 18.71it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.54it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:45<00:00, 14.97it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.46it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:22<00:00, 16.65it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [24:32<00:00,  2.36s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.73it/s]0s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:33<00:00, 10.51it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.30it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:45<00:00, 11.95it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.38it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [09:10<00:00,  7.76it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:10<00:00,  3.75it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [07:04<00:00,  5.61it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [43:15<00:00,  4.17s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.68it/s]9s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:32<00:00, 10.65it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.07it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [08:35<00:00,  9.40it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:17<00:00,  4.32it/s]

Testing seen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [08:42<00:00,  8.18it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:09<00:00,  3.96it/s]

Testing unseen_occ...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [04:30<00:00,  8.78it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [24:42<00:00,  2.38s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.68it/s]6s/it]

Testing seen...
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:30<00:00, 10.83it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.69it/s]

Testing unseen...
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [08:02<00:00, 10.06it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:17<00:00,  4.42it/s]

Testing seen_occ...
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  5.04it/s]
处理查询图像:  16%|█████████████████▋                                                                                                | 664/4272 [01:17<06:59,  8.60it/s]
 40%|█████████████████████████████████████████████████▌                                                                          | 10/25 [7:52:42<11:49:03, 2836.24s/it]
Traceback (most recent call last):
  File "train_new.py", line 417, in <module>
  File "train_new.py", line 337, in main
    # 清理梯度缓存，确保测试状态干净
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 213, in test
    for miniBatch in tqdm(query_dataloader, desc="处理查询图像"):
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/std.py", line 1191, in __iter__
    self.update(n - last_print_n)
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/std.py", line 1242, in update
    self.refresh(lock_args=self.lock_args)
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/std.py", line 1347, in refresh
    self.display()
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/std.py", line 1495, in display
    self.sp(self.__str__() if msg is None else msg)
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/std.py", line 459, in print_status
    fp_write('\r' + s + (' ' * max(last_len[0] - len_s, 0)))
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/std.py", line 452, in fp_write
    fp.write(str(s))
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/utils.py", line 196, in inner
    return func(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/lib/console_capture.py", line 155, in write_with_callbacks
    cb(s, n)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/lib/redirect.py", line 659, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/wandb_run.py", line 2302, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/wandb_run.py", line 401, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/wandb_run.py", line 1444, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/interface/interface.py", line 761, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/lib/sock_client.py", line 174, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/lib/sock_client.py", line 154, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/lib/sock_client.py", line 151, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/wandb/sdk/lib/sock_client.py", line 130, in _sendall_with_error_handle
    sent = self._sock.send(data)
KeyboardInterrupt
