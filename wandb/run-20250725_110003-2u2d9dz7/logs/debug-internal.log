{"time":"2025-07-25T11:00:03.133285811+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250725_110003-2u2d9dz7/logs/debug-core.log"}
{"time":"2025-07-25T11:00:03.26320972+08:00","level":"INFO","msg":"created new stream","id":"2u2d9dz7"}
{"time":"2025-07-25T11:00:03.263272957+08:00","level":"INFO","msg":"stream: started","id":"2u2d9dz7"}
{"time":"2025-07-25T11:00:03.263355922+08:00","level":"INFO","msg":"sender: started","stream_id":"2u2d9dz7"}
{"time":"2025-07-25T11:00:03.263399167+08:00","level":"INFO","msg":"handler: started","stream_id":"2u2d9dz7"}
{"time":"2025-07-25T11:00:03.263471007+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"2u2d9dz7"}
{"time":"2025-07-25T11:00:03.94152523+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-25T12:38:21.687309188+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/jialeren/pose-estimation-723/2u2d9dz7/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-07-25T14:37:21.715556657+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-25T15:46:01.223043503+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/2u2d9dz7/file_stream\": EOF"}
{"time":"2025-07-25T15:48:16.223233063+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/2u2d9dz7/file_stream\": EOF"}
{"time":"2025-07-25T16:51:17.53632141+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/2u2d9dz7/file_stream\": EOF"}
{"time":"2025-07-25T17:26:02.537317412+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/2u2d9dz7/file_stream\": EOF"}
{"time":"2025-07-25T18:03:17.536403312+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/2u2d9dz7/file_stream\": EOF"}
{"time":"2025-07-25T18:52:48.300652631+08:00","level":"INFO","msg":"stream: closing","id":"2u2d9dz7"}
{"time":"2025-07-25T18:52:48.300687101+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-25T18:52:48.301604088+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-25T18:52:49.552396171+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-25T18:52:50.162739197+08:00","level":"INFO","msg":"handler: closed","stream_id":"2u2d9dz7"}
{"time":"2025-07-25T18:52:50.162807221+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"2u2d9dz7"}
{"time":"2025-07-25T18:52:50.162873391+08:00","level":"INFO","msg":"sender: closed","stream_id":"2u2d9dz7"}
{"time":"2025-07-25T18:52:50.162890577+08:00","level":"INFO","msg":"stream: closed","id":"2u2d9dz7"}
