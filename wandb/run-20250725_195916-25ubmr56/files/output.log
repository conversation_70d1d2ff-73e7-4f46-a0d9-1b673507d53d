特征提取器可训练参数数量: 1.43M
偏移量预测器可训练参数数量: 0.96M
✅ 偏移量预测功能已启用
Hash编码器可训练参数数量: 0.14M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 1开始
   📊 Hash阶段优化器参数数量: 0.14M
初始阶段优化器参数数量: 2.39M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:41<00:00,  2.96s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:24<00:00,  6.89it/s]3s/it]

Testing seen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:23<00:00, 11.82it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.43it/s]
Epoch-0, seen -- Mean err: 4.52, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 1.85 minutes

Testing unseen...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:58<00:00, 13.54it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.87it/s]
Epoch-0, unseen -- Mean err: 4.55, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 6.23 minutes

Testing seen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:10<00:00, 13.77it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.87it/s]
Epoch-0, seen_occ -- Mean err: 12.23, Acc: 0.86, Rec : 0.94, Class and Pose  : 0.85
Validation time for epoch 0: 5.42 minutes

Testing unseen_occ...
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:09<00:00, 12.57it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:28<00:00,  2.84s/it]
Epoch-0, unseen_occ -- Mean err: 9.10, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 0: 3.31 minutes

🔄 Epoch 1: 切换到Hash训练阶段
   🔒 冻结主要模块参数（model, offset_predictor）
   🔓 只训练Hash编码器参数
   📊 Hash优化器参数数量: 0.14M
   📈 Hash训练学习率: 1e-05
   ✅ 优化器已切换到Hash训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:27<00:00,  6.12it/s]8s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:19<00:00, 12.28it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.54it/s]
Epoch-1, seen -- Mean err: 8.59, Acc: 0.91, Rec : 0.93, Class and Pose  : 0.91
Validation time for epoch 1: 1.87 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:09<00:00, 13.13it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.31it/s]
Epoch-1, unseen -- Mean err: 7.72, Acc: 0.93, Rec : 0.97, Class and Pose  : 0.93
Validation time for epoch 1: 6.40 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:26<00:00, 13.07it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.51it/s]
Epoch-1, seen_occ -- Mean err: 19.72, Acc: 0.70, Rec : 0.81, Class and Pose  : 0.68
Validation time for epoch 1: 5.71 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:47<00:00, 10.44it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [28:44<00:00,  2.77s/it]
Epoch-1, unseen_occ -- Mean err: 19.16, Acc: 0.69, Rec : 0.93, Class and Pose  : 0.68
Validation time for epoch 1: 3.96 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.76it/s]3s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:19<00:00, 12.38it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.43it/s]
Epoch-2, seen -- Mean err: 13.22, Acc: 0.81, Rec : 0.84, Class and Pose  : 0.80
Validation time for epoch 2: 1.78 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:09<00:00, 13.13it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.48it/s]
Epoch-2, unseen -- Mean err: 10.68, Acc: 0.89, Rec : 0.94, Class and Pose  : 0.88
Validation time for epoch 2: 6.41 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:26<00:00, 13.07it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:09<00:00,  4.16it/s]
Epoch-2, seen_occ -- Mean err: 21.60, Acc: 0.64, Rec : 0.77, Class and Pose  : 0.62
Validation time for epoch 2: 5.70 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [04:00<00:00,  9.90it/s]
 12%|███████████████                                                                                                              | 3/25 [2:22:11<17:20:54, 2838.84s/it][34m[1mwandb[0m: [33mWARNING[0m Fatal error while uploading data. Some run data will not be synced, but it will still be written to disk. Use `wandb sync` at the end of the run to try uploading.
Epoch-2, unseen_occ -- Mean err: 23.99, Acc: 0.63, Rec : 0.89, Class and Pose  : 0.63
Validation time for epoch 2: 4.21 minutes
 79%|█████████████████████████████████████████████████████████████████████████████████████████████████████▊                           | 492/623 [24:00<04:33,  2.09s/it]
 81%|████████████████████████████████████████████████████████████████████████████████████████████████████████▎                        | 504/623 [24:26<03:56,  1.99s/it]
