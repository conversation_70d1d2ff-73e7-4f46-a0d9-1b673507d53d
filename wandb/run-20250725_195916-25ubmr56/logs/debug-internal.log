{"time":"2025-07-25T19:59:16.18582514+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250725_195916-25ubmr56/logs/debug-core.log"}
{"time":"2025-07-25T19:59:16.319840213+08:00","level":"INFO","msg":"created new stream","id":"25ubmr56"}
{"time":"2025-07-25T19:59:16.319906309+08:00","level":"INFO","msg":"stream: started","id":"25ubmr56"}
{"time":"2025-07-25T19:59:16.319964833+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"25ubmr56"}
{"time":"2025-07-25T19:59:16.320197102+08:00","level":"INFO","msg":"handler: started","stream_id":"25ubmr56"}
{"time":"2025-07-25T19:59:16.320232906+08:00","level":"INFO","msg":"sender: started","stream_id":"25ubmr56"}
{"time":"2025-07-25T19:59:17.009190708+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-25T20:22:17.82349192+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/25ubmr56/file_stream\": unexpected EOF"}
{"time":"2025-07-25T20:22:56.593049994+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/25ubmr56/file_stream\": unexpected EOF"}
{"time":"2025-07-25T20:26:03.140599071+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/25ubmr56/file_stream\": unexpected EOF"}
{"time":"2025-07-25T20:39:23.77033132+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/25ubmr56/file_stream\": local error: tls: bad record MAC"}
{"time":"2025-07-25T21:19:32.041499282+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/25ubmr56/file_stream\": EOF"}
{"time":"2025-07-25T22:32:17.045785979+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/25ubmr56/file_stream\": EOF"}
{"time":"2025-07-25T22:45:27.502769972+08:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/files/jialeren/pose-estimation-723/25ubmr56/file_stream"}
{"time":"2025-07-25T22:45:27.502860126+08:00","level":"ERROR+4","msg":"filestream: fatal error: filestream: failed to upload: 404 Not Found path=files/jialeren/pose-estimation-723/25ubmr56/file_stream: {\"error\":\"run pose-estimation-723/25ubmr56 not found while streaming file\"}"}
