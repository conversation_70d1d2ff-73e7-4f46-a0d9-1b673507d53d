特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.14M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 1开始
   📊 Hash阶段优化器参数数量: 0.14M
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:18<00:00,  2.92s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:32<00:00,  5.18it/s]9s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:21<00:00, 12.08it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.10it/s]
Epoch-0, seen -- Mean err: 4.55, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 1.98 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:46<00:00, 14.01it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.26it/s]
Epoch-0, unseen -- Mean err: 4.61, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 6.04 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:55<00:00, 14.47it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.87it/s]
Epoch-0, seen_occ -- Mean err: 12.92, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.85
Validation time for epoch 0: 5.18 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:36<00:00, 15.22it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:56<00:00,  2.88s/it]
Epoch-0, unseen_occ -- Mean err: 9.27, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 0: 2.77 minutes

🔄 Epoch 1: 切换到Hash训练阶段
   🔓 只训练Hash编码器参数（保持主要模块梯度传播）
   ✅ 主要模块参数保持可梯度传播，确保任务损失能指导Hash学习
   📊 Hash优化器参数数量: 0.14M
   📈 Hash训练学习率: 1e-05
   ✅ 优化器已切换到Hash训练模式，梯度传播完整
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:34<00:00,  4.89it/s]5s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:41<00:00,  9.68it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.64it/s]
Epoch-1, seen -- Mean err: 8.05, Acc: 0.92, Rec : 0.94, Class and Pose  : 0.92
Validation time for epoch 1: 2.33 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:05<00:00, 13.26it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.31it/s]
Epoch-1, unseen -- Mean err: 8.98, Acc: 0.91, Rec : 0.95, Class and Pose  : 0.91
Validation time for epoch 1: 6.43 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:12<00:00, 13.69it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.63it/s]
Epoch-1, seen_occ -- Mean err: 20.32, Acc: 0.69, Rec : 0.77, Class and Pose  : 0.66
Validation time for epoch 1: 5.47 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:40<00:00, 14.80it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:01<00:00,  2.80s/it]
Epoch-1, unseen_occ -- Mean err: 17.82, Acc: 0.71, Rec : 0.95, Class and Pose  : 0.70
Validation time for epoch 1: 2.86 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:34<00:00,  4.90it/s]1s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:39<00:00,  9.84it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.58it/s]
Epoch-2, seen -- Mean err: 14.75, Acc: 0.78, Rec : 0.81, Class and Pose  : 0.78
Validation time for epoch 2: 2.29 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:41<00:00, 12.08it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.61it/s]
Epoch-2, unseen -- Mean err: 13.28, Acc: 0.84, Rec : 0.89, Class and Pose  : 0.84
Validation time for epoch 2: 7.04 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:54<00:00, 14.50it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.53it/s]
Epoch-2, seen_occ -- Mean err: 23.45, Acc: 0.60, Rec : 0.70, Class and Pose  : 0.57
Validation time for epoch 2: 5.16 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:45<00:00, 14.40it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [27:42<00:00,  2.67s/it]
Epoch-2, unseen_occ -- Mean err: 21.07, Acc: 0.65, Rec : 0.93, Class and Pose  : 0.65
Validation time for epoch 2: 2.92 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:36<00:00,  4.68it/s]7s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:44<00:00,  9.35it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.69it/s]
Epoch-3, seen -- Mean err: 15.89, Acc: 0.75, Rec : 0.79, Class and Pose  : 0.74
Validation time for epoch 3: 2.43 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [07:22<00:00, 10.97it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.15it/s]
Epoch-3, unseen -- Mean err: 12.67, Acc: 0.84, Rec : 0.90, Class and Pose  : 0.84
Validation time for epoch 3: 7.69 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:04<00:00, 14.02it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.30it/s]
Epoch-3, seen_occ -- Mean err: 24.47, Acc: 0.58, Rec : 0.69, Class and Pose  : 0.55
Validation time for epoch 3: 5.34 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:45<00:00, 14.36it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [26:30<00:00,  2.55s/it]
Epoch-3, unseen_occ -- Mean err: 20.04, Acc: 0.64, Rec : 0.93, Class and Pose  : 0.64
Validation time for epoch 3: 2.94 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:34<00:00,  4.97it/s]0s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:36<00:00, 10.19it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:15<00:00,  5.03it/s]
Epoch-4, seen -- Mean err: 10.91, Acc: 0.86, Rec : 0.89, Class and Pose  : 0.86
Validation time for epoch 4: 2.23 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [07:43<00:00, 10.45it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.52it/s]
Epoch-4, unseen -- Mean err: 8.94, Acc: 0.91, Rec : 0.97, Class and Pose  : 0.91
Validation time for epoch 4: 8.06 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:53<00:00, 12.09it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.80it/s]
Epoch-4, seen_occ -- Mean err: 22.23, Acc: 0.63, Rec : 0.75, Class and Pose  : 0.61
Validation time for epoch 4: 6.23 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:45<00:00, 14.35it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [25:54<00:00,  2.49s/it]
Epoch-4, unseen_occ -- Mean err: 20.53, Acc: 0.65, Rec : 0.94, Class and Pose  : 0.65
Validation time for epoch 4: 2.93 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.85it/s]7s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:35<00:00, 10.26it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.55it/s]
Epoch-5, seen -- Mean err: 14.08, Acc: 0.80, Rec : 0.83, Class and Pose  : 0.80
Validation time for epoch 5: 2.24 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [07:38<00:00, 10.56it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:15<00:00,  4.78it/s]
Epoch-5, unseen -- Mean err: 11.24, Acc: 0.85, Rec : 0.94, Class and Pose  : 0.85
Validation time for epoch 5: 7.99 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [06:26<00:00, 11.04it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.84it/s]
Epoch-5, seen_occ -- Mean err: 23.64, Acc: 0.60, Rec : 0.74, Class and Pose  : 0.58
Validation time for epoch 5: 6.76 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:48<00:00, 14.09it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [24:34<00:00,  2.37s/it]
Epoch-5, unseen_occ -- Mean err: 20.74, Acc: 0.63, Rec : 0.95, Class and Pose  : 0.62
Validation time for epoch 5: 2.99 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:34<00:00,  4.92it/s]8s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:37<00:00, 10.09it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.47it/s]
Epoch-6, seen -- Mean err: 12.07, Acc: 0.85, Rec : 0.88, Class and Pose  : 0.84
Validation time for epoch 6: 2.25 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [07:43<00:00, 10.47it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:15<00:00,  4.87it/s]
Epoch-6, unseen -- Mean err: 10.05, Acc: 0.87, Rec : 0.97, Class and Pose  : 0.87
Validation time for epoch 6: 8.06 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [06:50<00:00, 10.41it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.39it/s]
Epoch-6, seen_occ -- Mean err: 22.97, Acc: 0.62, Rec : 0.77, Class and Pose  : 0.60
Validation time for epoch 6: 7.16 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:04<00:00, 12.86it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [23:21<00:00,  2.25s/it]
Epoch-6, unseen_occ -- Mean err: 21.18, Acc: 0.62, Rec : 0.95, Class and Pose  : 0.62
Validation time for epoch 6: 3.30 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:34<00:00,  4.95it/s]2s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:45<00:00,  9.32it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:15<00:00,  4.76it/s]
Epoch-7, seen -- Mean err: 11.97, Acc: 0.84, Rec : 0.89, Class and Pose  : 0.83
Validation time for epoch 7: 2.39 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [07:42<00:00, 10.48it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.54it/s]
Epoch-7, unseen -- Mean err: 10.46, Acc: 0.86, Rec : 0.97, Class and Pose  : 0.86
Validation time for epoch 7: 8.03 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [06:48<00:00, 10.45it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.60it/s]
Epoch-7, seen_occ -- Mean err: 23.86, Acc: 0.60, Rec : 0.77, Class and Pose  : 0.58
Validation time for epoch 7: 7.17 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:33<00:00, 11.15it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [23:07<00:00,  2.23s/it]
Epoch-7, unseen_occ -- Mean err: 22.28, Acc: 0.60, Rec : 0.96, Class and Pose  : 0.60
Validation time for epoch 7: 3.76 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:24<00:00,  6.89it/s]7s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:26<00:00, 11.36it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:17<00:00,  4.47it/s]
Epoch-8, seen -- Mean err: 10.08, Acc: 0.88, Rec : 0.92, Class and Pose  : 0.87
Validation time for epoch 8: 1.92 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [07:38<00:00, 10.58it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.49it/s]
Epoch-8, unseen -- Mean err: 8.64, Acc: 0.91, Rec : 0.99, Class and Pose  : 0.91
Validation time for epoch 8: 7.97 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [06:46<00:00, 10.50it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:09<00:00,  4.22it/s]
Epoch-8, seen_occ -- Mean err: 23.50, Acc: 0.62, Rec : 0.76, Class and Pose  : 0.60
Validation time for epoch 8: 7.11 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:32<00:00, 11.18it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [24:28<00:00,  2.36s/it]
Epoch-8, unseen_occ -- Mean err: 23.97, Acc: 0.61, Rec : 0.96, Class and Pose  : 0.61
Validation time for epoch 8: 3.75 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.61it/s]8s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:14<00:00, 13.22it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.26it/s]
Epoch-9, seen -- Mean err: 12.98, Acc: 0.81, Rec : 0.86, Class and Pose  : 0.81
Validation time for epoch 9: 1.72 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [07:02<00:00, 11.49it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.59it/s]
Epoch-9, unseen -- Mean err: 10.90, Acc: 0.85, Rec : 0.97, Class and Pose  : 0.85
Validation time for epoch 9: 7.30 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [06:49<00:00, 10.44it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.28it/s]
Epoch-9, seen_occ -- Mean err: 25.13, Acc: 0.56, Rec : 0.74, Class and Pose  : 0.54
Validation time for epoch 9: 7.15 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:32<00:00, 11.21it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [25:24<00:00,  2.45s/it]
Epoch-9, unseen_occ -- Mean err: 24.45, Acc: 0.56, Rec : 0.96, Class and Pose  : 0.56
Validation time for epoch 9: 3.74 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:24<00:00,  6.83it/s]1s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:14<00:00, 13.08it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.31it/s]
Epoch-10, seen -- Mean err: 14.07, Acc: 0.78, Rec : 0.84, Class and Pose  : 0.78
Validation time for epoch 10: 1.74 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:18<00:00, 12.82it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.62it/s]
Epoch-10, unseen -- Mean err: 12.62, Acc: 0.81, Rec : 0.95, Class and Pose  : 0.81
Validation time for epoch 10: 6.58 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [06:40<00:00, 10.65it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.56it/s]
Epoch-10, seen_occ -- Mean err: 25.64, Acc: 0.55, Rec : 0.73, Class and Pose  : 0.53
Validation time for epoch 10: 7.02 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:35<00:00, 11.03it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [26:52<00:00,  2.59s/it]
Epoch-10, unseen_occ -- Mean err: 26.14, Acc: 0.55, Rec : 0.96, Class and Pose  : 0.55
Validation time for epoch 10: 3.80 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:24<00:00,  6.84it/s]0s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:14<00:00, 13.18it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.49it/s]
Epoch-11, seen -- Mean err: 11.28, Acc: 0.85, Rec : 0.91, Class and Pose  : 0.85
Validation time for epoch 11: 1.71 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:50<00:00, 13.84it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.89it/s]
Epoch-11, unseen -- Mean err: 9.13, Acc: 0.88, Rec : 0.99, Class and Pose  : 0.88
Validation time for epoch 11: 6.09 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [06:16<00:00, 11.36it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.42it/s]
Epoch-11, seen_occ -- Mean err: 23.34, Acc: 0.61, Rec : 0.78, Class and Pose  : 0.59
Validation time for epoch 11: 6.55 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:40<00:00, 10.80it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [27:49<00:00,  2.68s/it]
Epoch-11, unseen_occ -- Mean err: 23.50, Acc: 0.59, Rec : 0.96, Class and Pose  : 0.59
Validation time for epoch 11: 3.87 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.70it/s]5s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:15<00:00, 13.00it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:14<00:00,  5.08it/s]
Epoch-12, seen -- Mean err: 12.28, Acc: 0.83, Rec : 0.88, Class and Pose  : 0.82
Validation time for epoch 12: 1.74 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:44<00:00, 14.07it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.42it/s]
Epoch-12, unseen -- Mean err: 11.07, Acc: 0.85, Rec : 0.97, Class and Pose  : 0.85
Validation time for epoch 12: 6.05 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:32<00:00, 12.83it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:09<00:00,  4.14it/s]
Epoch-12, seen_occ -- Mean err: 24.46, Acc: 0.57, Rec : 0.76, Class and Pose  : 0.56
Validation time for epoch 12: 5.80 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:37<00:00, 10.92it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:17<00:00,  2.82s/it]
Epoch-12, unseen_occ -- Mean err: 24.58, Acc: 0.57, Rec : 0.96, Class and Pose  : 0.57
Validation time for epoch 12: 3.84 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:23<00:00,  7.08it/s]1s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:16<00:00, 12.80it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.24it/s]
Epoch-13, seen -- Mean err: 11.12, Acc: 0.85, Rec : 0.90, Class and Pose  : 0.84
Validation time for epoch 13: 1.73 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:52<00:00, 13.76it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.41it/s]
Epoch-13, unseen -- Mean err: 10.23, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 13: 6.13 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:59<00:00, 14.25it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.60it/s]
Epoch-13, seen_occ -- Mean err: 23.94, Acc: 0.60, Rec : 0.77, Class and Pose  : 0.58
Validation time for epoch 13: 5.24 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:32<00:00, 11.20it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:48<00:00,  2.87s/it]
Epoch-13, unseen_occ -- Mean err: 23.86, Acc: 0.59, Rec : 0.96, Class and Pose  : 0.58
Validation time for epoch 13: 3.71 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:24<00:00,  6.89it/s]7s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:14<00:00, 13.26it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.37it/s]
Epoch-14, seen -- Mean err: 15.09, Acc: 0.77, Rec : 0.82, Class and Pose  : 0.76
Validation time for epoch 14: 1.71 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:45<00:00, 14.04it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.18it/s]
Epoch-14, unseen -- Mean err: 12.12, Acc: 0.82, Rec : 0.95, Class and Pose  : 0.82
Validation time for epoch 14: 6.01 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:54<00:00, 14.53it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.01it/s]
Epoch-14, seen_occ -- Mean err: 25.35, Acc: 0.55, Rec : 0.73, Class and Pose  : 0.53
Validation time for epoch 14: 5.17 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:42<00:00, 14.63it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:47<00:00,  2.96s/it]
Epoch-14, unseen_occ -- Mean err: 24.87, Acc: 0.54, Rec : 0.97, Class and Pose  : 0.53
Validation time for epoch 14: 2.88 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.74it/s]8s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:18<00:00, 12.53it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:13<00:00,  5.64it/s]
Epoch-15, seen -- Mean err: 11.61, Acc: 0.84, Rec : 0.89, Class and Pose  : 0.83
Validation time for epoch 15: 1.97 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:40<00:00, 14.24it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.45it/s]
Epoch-15, unseen -- Mean err: 10.72, Acc: 0.86, Rec : 0.97, Class and Pose  : 0.86
Validation time for epoch 15: 5.95 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:09<00:00, 13.82it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.21it/s]
Epoch-15, seen_occ -- Mean err: 24.44, Acc: 0.58, Rec : 0.77, Class and Pose  : 0.57
Validation time for epoch 15: 5.42 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:42<00:00, 14.63it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:11<00:00,  2.91s/it]
Epoch-15, unseen_occ -- Mean err: 23.83, Acc: 0.58, Rec : 0.97, Class and Pose  : 0.58
Validation time for epoch 15: 2.86 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.85it/s]9s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:40<00:00,  9.78it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.52it/s]
Epoch-16, seen -- Mean err: 10.42, Acc: 0.87, Rec : 0.92, Class and Pose  : 0.87
Validation time for epoch 16: 2.34 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:48<00:00, 13.90it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.28it/s]
Epoch-16, unseen -- Mean err: 9.56, Acc: 0.88, Rec : 0.99, Class and Pose  : 0.88
Validation time for epoch 16: 6.15 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:58<00:00, 14.32it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.82it/s]
Epoch-16, seen_occ -- Mean err: 24.11, Acc: 0.60, Rec : 0.78, Class and Pose  : 0.59
Validation time for epoch 16: 5.25 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:34<00:00, 15.43it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:12<00:00,  2.81s/it]
Epoch-16, unseen_occ -- Mean err: 23.50, Acc: 0.60, Rec : 0.97, Class and Pose  : 0.59
Validation time for epoch 16: 2.74 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:36<00:00,  4.65it/s]9s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:41<00:00,  9.65it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:17<00:00,  4.46it/s]
Epoch-17, seen -- Mean err: 10.15, Acc: 0.88, Rec : 0.93, Class and Pose  : 0.88
Validation time for epoch 17: 2.37 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:45<00:00, 11.96it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.13it/s]
Epoch-17, unseen -- Mean err: 9.89, Acc: 0.88, Rec : 0.99, Class and Pose  : 0.88
Validation time for epoch 17: 7.10 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:11<00:00, 13.73it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.14it/s]
Epoch-17, seen_occ -- Mean err: 23.62, Acc: 0.61, Rec : 0.79, Class and Pose  : 0.60
Validation time for epoch 17: 5.46 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:39<00:00, 14.89it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [27:41<00:00,  2.67s/it]
Epoch-17, unseen_occ -- Mean err: 23.72, Acc: 0.59, Rec : 0.97, Class and Pose  : 0.59
Validation time for epoch 17: 2.82 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:34<00:00,  4.92it/s]0s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:44<00:00,  9.43it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.51it/s]
Epoch-18, seen -- Mean err: 11.16, Acc: 0.86, Rec : 0.91, Class and Pose  : 0.85
Validation time for epoch 18: 2.36 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [07:24<00:00, 10.90it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.25it/s]
Epoch-18, unseen -- Mean err: 10.23, Acc: 0.86, Rec : 0.99, Class and Pose  : 0.86
Validation time for epoch 18: 7.74 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像:  45%|██████████████████████████████████████████████████▊                                                              | 1920/4272 [02:14<02:45, 14.23it/s]
 72%|█████████████████████████████████████████████████████████████████████████████████████████▎                                  | 18/25 [14:26:16<5:36:53, 2887.60s/it]
Traceback (most recent call last):
  File "train_new.py", line 414, in <module>
    # 如果有hash_encoder，也保存其权重
  File "train_new.py", line 334, in main
    if (epoch + 1) % 1 == 0:
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 159, in test
    feature_query = hash_encoder.encode_features(original_query_features)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/learnable_hash.py", line 707, in encode_features
    pose_hash = self.pose_hash_encoder(features['pose_feature'])
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/learnable_hash.py", line 89, in forward
    hash_map = self.pointwise_encoder(x_attended)  # [B, hash_bits, H, W]
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/container.py", line 139, in forward
    input = module(input)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/activation.py", line 354, in forward
    return torch.tanh(input)
KeyboardInterrupt
