{"time":"2025-07-25T22:51:25.246925423+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250725_225125-tjeuel7d/logs/debug-core.log"}
{"time":"2025-07-25T22:51:25.487692102+08:00","level":"INFO","msg":"created new stream","id":"tjeuel7d"}
{"time":"2025-07-25T22:51:25.487757724+08:00","level":"INFO","msg":"stream: started","id":"tjeuel7d"}
{"time":"2025-07-25T22:51:25.493772524+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"tjeuel7d"}
{"time":"2025-07-25T22:51:25.494002693+08:00","level":"INFO","msg":"handler: started","stream_id":"tjeuel7d"}
{"time":"2025-07-25T22:51:25.4944335+08:00","level":"INFO","msg":"sender: started","stream_id":"tjeuel7d"}
{"time":"2025-07-25T22:51:26.182429381+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-25T23:00:47.307233622+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-25T23:11:17.314894151+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-25T23:24:02.305107554+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-25T23:40:17.305128471+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-25T23:43:17.305419134+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T00:25:47.308326436+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T00:25:54.743157901+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T00:26:17.305025069+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T00:28:17.305340645+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T01:32:32.318284652+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T02:07:57.501421321+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": local error: tls: bad record MAC"}
{"time":"2025-07-26T02:11:07.100614405+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T02:11:14.240942008+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T02:11:23.865552279+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T02:11:37.505064916+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T02:11:39.320217879+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-26T02:12:02.487007791+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T02:12:11.60154525+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-26T02:12:23.39478813+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": local error: tls: bad record MAC"}
{"time":"2025-07-26T04:08:31.321559986+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T07:19:46.290771442+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T09:16:16.290769271+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T11:30:31.292462754+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T12:01:16.291053628+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T12:57:16.290523333+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/tjeuel7d/file_stream\": EOF"}
{"time":"2025-07-26T13:17:45.482405076+08:00","level":"INFO","msg":"stream: closing","id":"tjeuel7d"}
{"time":"2025-07-26T13:17:45.482438281+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-26T13:17:45.483482892+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-26T13:17:47.049337004+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-26T13:17:47.643223933+08:00","level":"INFO","msg":"handler: closed","stream_id":"tjeuel7d"}
{"time":"2025-07-26T13:17:47.643275603+08:00","level":"INFO","msg":"sender: closed","stream_id":"tjeuel7d"}
{"time":"2025-07-26T13:17:47.64327129+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"tjeuel7d"}
{"time":"2025-07-26T13:17:47.643348898+08:00","level":"INFO","msg":"stream: closed","id":"tjeuel7d"}
