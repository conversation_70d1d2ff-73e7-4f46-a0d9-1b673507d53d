{"time":"2025-07-25T22:51:26.936621928+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250725_225126-td00bfxx/logs/debug-core.log"}
{"time":"2025-07-25T22:51:27.181047022+08:00","level":"INFO","msg":"created new stream","id":"td00bfxx"}
{"time":"2025-07-25T22:51:27.181116601+08:00","level":"INFO","msg":"stream: started","id":"td00bfxx"}
{"time":"2025-07-25T22:51:27.181204753+08:00","level":"INFO","msg":"handler: started","stream_id":"td00bfxx"}
{"time":"2025-07-25T22:51:27.181242766+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"td00bfxx"}
{"time":"2025-07-25T22:51:27.181324608+08:00","level":"INFO","msg":"sender: started","stream_id":"td00bfxx"}
{"time":"2025-07-25T22:51:27.874226011+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-25T23:00:03.870677452+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-25T23:05:48.868997643+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-25T23:24:48.869071565+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T00:24:48.878596056+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T00:24:56.083087229+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T00:25:05.833729835+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T00:27:43.046881129+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T00:43:43.047048403+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T00:47:13.047087741+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T02:10:28.049136445+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T02:10:43.049531034+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T02:10:58.046857732+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T02:11:13.046913188+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T02:11:20.639076998+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T02:11:30.016542649+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T02:11:44.279030569+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T02:11:52.743999074+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-07-26T02:12:00.21500636+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-07-26T02:12:08.333493398+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T02:12:10.104123081+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-07-26T02:12:24.470183623+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-07-26T08:22:23.394464274+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T11:06:53.39751084+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/td00bfxx/file_stream\": EOF"}
{"time":"2025-07-26T13:17:49.123423683+08:00","level":"INFO","msg":"stream: closing","id":"td00bfxx"}
{"time":"2025-07-26T13:17:49.123455581+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-26T13:17:49.124280913+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-26T13:17:50.91636332+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-26T13:17:51.539634704+08:00","level":"INFO","msg":"handler: closed","stream_id":"td00bfxx"}
{"time":"2025-07-26T13:17:51.539683577+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"td00bfxx"}
{"time":"2025-07-26T13:17:51.539763561+08:00","level":"INFO","msg":"sender: closed","stream_id":"td00bfxx"}
{"time":"2025-07-26T13:17:51.539775474+08:00","level":"INFO","msg":"stream: closed","id":"td00bfxx"}
