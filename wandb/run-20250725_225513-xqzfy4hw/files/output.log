特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.14M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.14M
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████| 623/623 [32:01<00:00,  3.08s/it]

🔄 Epoch 0: 切换到Hash联合训练阶段
   🔓 训练所有参数：主要模型 + Hash编码器
   ✅ 真正的端到端联合训练
   📊 联合训练参数统计:
      - 主要模型: 1.43M
      - Hash编码器: 0.14M
      - 总计: 1.56M
   📈 联合训练学习率: 0.0001
   ✅ 优化器已切换到联合训练模式
处理模板数据: 100%|███████████████████████████████| 170/170 [00:27<00:00,  6.15it/s]0s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:23<00:00, 11.75it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.56it/s]
Epoch-0, seen -- Mean err: 4.84, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 1.90 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:56<00:00, 13.61it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.07it/s]
Epoch-0, unseen -- Mean err: 4.80, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 6.20 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:11<00:00, 13.74it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.59it/s]
Epoch-0, seen_occ -- Mean err: 13.44, Acc: 0.83, Rec : 0.91, Class and Pose  : 0.82
Validation time for epoch 0: 5.45 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:44<00:00, 14.46it/s]
100%|█████████████████████████████████████████████| 623/623 [31:03<00:00,  2.99s/it]
Epoch-0, unseen_occ -- Mean err: 10.38, Acc: 0.86, Rec : 0.97, Class and Pose  : 0.86
Validation time for epoch 0: 2.90 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:25<00:00,  6.64it/s]8s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:21<00:00, 12.08it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.40it/s]
Epoch-1, seen -- Mean err: 6.45, Acc: 0.95, Rec : 0.97, Class and Pose  : 0.95
Validation time for epoch 1: 1.84 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:56<00:00, 13.59it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.42it/s]
Epoch-1, unseen -- Mean err: 8.24, Acc: 0.92, Rec : 0.98, Class and Pose  : 0.92
Validation time for epoch 1: 6.21 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:23<00:00, 13.22it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.86it/s]
Epoch-1, seen_occ -- Mean err: 18.06, Acc: 0.73, Rec : 0.84, Class and Pose  : 0.72
Validation time for epoch 1: 5.63 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:49<00:00, 14.05it/s]
100%|█████████████████████████████████████████████| 623/623 [31:00<00:00,  2.99s/it]
Epoch-1, unseen_occ -- Mean err: 17.47, Acc: 0.74, Rec : 0.93, Class and Pose  : 0.74
Validation time for epoch 1: 2.99 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:25<00:00,  6.62it/s]4s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:19<00:00, 12.42it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.57it/s]
Epoch-2, seen -- Mean err: 7.23, Acc: 0.93, Rec : 0.94, Class and Pose  : 0.92
Validation time for epoch 2: 1.80 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:55<00:00, 13.65it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.11it/s]
Epoch-2, unseen -- Mean err: 9.06, Acc: 0.91, Rec : 0.97, Class and Pose  : 0.91
Validation time for epoch 2: 6.16 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:09<00:00, 13.79it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.80it/s]
Epoch-2, seen_occ -- Mean err: 21.24, Acc: 0.67, Rec : 0.79, Class and Pose  : 0.66
Validation time for epoch 2: 5.44 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:39<00:00, 14.94it/s]
100%|█████████████████████████████████████████████| 623/623 [31:33<00:00,  3.04s/it]
Epoch-2, unseen_occ -- Mean err: 22.06, Acc: 0.69, Rec : 0.87, Class and Pose  : 0.68
Validation time for epoch 2: 2.82 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:25<00:00,  6.68it/s]0s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:22<00:00, 11.96it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.35it/s]
Epoch-3, seen -- Mean err: 7.18, Acc: 0.95, Rec : 0.96, Class and Pose  : 0.94
Validation time for epoch 3: 1.86 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:56<00:00, 13.59it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.03it/s]
Epoch-3, unseen -- Mean err: 9.85, Acc: 0.90, Rec : 0.96, Class and Pose  : 0.90
Validation time for epoch 3: 6.19 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:07<00:00, 13.87it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.51it/s]
Epoch-3, seen_occ -- Mean err: 21.44, Acc: 0.67, Rec : 0.79, Class and Pose  : 0.65
Validation time for epoch 3: 5.41 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:45<00:00, 14.37it/s]
100%|█████████████████████████████████████████████| 623/623 [31:12<00:00,  3.01s/it]
Epoch-3, unseen_occ -- Mean err: 22.99, Acc: 0.69, Rec : 0.86, Class and Pose  : 0.68
Validation time for epoch 3: 2.93 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:26<00:00,  6.35it/s]0s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:22<00:00, 11.85it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.18it/s]
Epoch-4, seen -- Mean err: 7.31, Acc: 0.94, Rec : 0.95, Class and Pose  : 0.94
Validation time for epoch 4: 1.90 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:58<00:00, 13.52it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.08it/s]
Epoch-4, unseen -- Mean err: 9.61, Acc: 0.90, Rec : 0.98, Class and Pose  : 0.90
Validation time for epoch 4: 6.23 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:11<00:00, 13.73it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  6.14it/s]
Epoch-4, seen_occ -- Mean err: 22.16, Acc: 0.66, Rec : 0.79, Class and Pose  : 0.64
Validation time for epoch 4: 5.45 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:39<00:00, 14.86it/s]
100%|█████████████████████████████████████████████| 623/623 [31:21<00:00,  3.02s/it]
Epoch-4, unseen_occ -- Mean err: 20.08, Acc: 0.70, Rec : 0.89, Class and Pose  : 0.69
Validation time for epoch 4: 2.84 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.68it/s]3s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:16<00:00, 12.85it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:15<00:00,  4.94it/s]
Epoch-5, seen -- Mean err: 7.01, Acc: 0.95, Rec : 0.96, Class and Pose  : 0.94
Validation time for epoch 5: 1.94 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:52<00:00, 13.75it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:17<00:00,  4.28it/s]
Epoch-5, unseen -- Mean err: 9.59, Acc: 0.91, Rec : 0.97, Class and Pose  : 0.91
Validation time for epoch 5: 6.19 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:07<00:00, 13.89it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  6.09it/s]
Epoch-5, seen_occ -- Mean err: 22.10, Acc: 0.65, Rec : 0.79, Class and Pose  : 0.63
Validation time for epoch 5: 5.49 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:46<00:00, 14.31it/s]
100%|█████████████████████████████████████████████| 623/623 [31:25<00:00,  3.03s/it]
Epoch-5, unseen_occ -- Mean err: 20.04, Acc: 0.69, Rec : 0.88, Class and Pose  : 0.68
Validation time for epoch 5: 2.93 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:35<00:00,  4.76it/s]8s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:18<00:00, 12.43it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:15<00:00,  4.83it/s]
Epoch-6, seen -- Mean err: 9.17, Acc: 0.90, Rec : 0.92, Class and Pose  : 0.89
Validation time for epoch 6: 1.96 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:59<00:00, 13.50it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:13<00:00,  5.80it/s]
Epoch-6, unseen -- Mean err: 11.64, Acc: 0.86, Rec : 0.94, Class and Pose  : 0.86
Validation time for epoch 6: 6.31 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:02<00:00, 14.13it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:08<00:00,  4.70it/s]
Epoch-6, seen_occ -- Mean err: 21.24, Acc: 0.66, Rec : 0.78, Class and Pose  : 0.64
Validation time for epoch 6: 5.31 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:37<00:00, 15.06it/s]
100%|█████████████████████████████████████████████| 623/623 [31:05<00:00,  2.99s/it]
Epoch-6, unseen_occ -- Mean err: 21.13, Acc: 0.64, Rec : 0.85, Class and Pose  : 0.62
Validation time for epoch 6: 2.83 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:32<00:00,  5.21it/s]3s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:23<00:00, 11.72it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.20it/s]
Epoch-7, seen -- Mean err: 7.11, Acc: 0.94, Rec : 0.96, Class and Pose  : 0.93
Validation time for epoch 7: 2.01 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:47<00:00, 13.95it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.48it/s]
Epoch-7, unseen -- Mean err: 10.63, Acc: 0.89, Rec : 0.97, Class and Pose  : 0.89
Validation time for epoch 7: 6.06 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:18<00:00, 13.43it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:07<00:00,  5.34it/s]
Epoch-7, seen_occ -- Mean err: 21.50, Acc: 0.66, Rec : 0.81, Class and Pose  : 0.65
Validation time for epoch 7: 5.56 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:41<00:00, 14.73it/s]
100%|█████████████████████████████████████████████| 623/623 [30:44<00:00,  2.96s/it]
Epoch-7, unseen_occ -- Mean err: 21.93, Acc: 0.68, Rec : 0.89, Class and Pose  : 0.68
Validation time for epoch 7: 2.86 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:37<00:00,  4.54it/s]2s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:35<00:00, 10.30it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.12it/s]
Epoch-8, seen -- Mean err: 7.72, Acc: 0.93, Rec : 0.94, Class and Pose  : 0.92
Validation time for epoch 8: 2.28 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:54<00:00, 13.66it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.41it/s]
Epoch-8, unseen -- Mean err: 10.94, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 8: 6.19 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:13<00:00, 13.62it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.52it/s]
Epoch-8, seen_occ -- Mean err: 23.16, Acc: 0.63, Rec : 0.80, Class and Pose  : 0.61
Validation time for epoch 8: 5.48 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:43<00:00, 14.53it/s]
100%|█████████████████████████████████████████████| 623/623 [30:38<00:00,  2.95s/it]
Epoch-8, unseen_occ -- Mean err: 24.97, Acc: 0.63, Rec : 0.91, Class and Pose  : 0.62
Validation time for epoch 8: 2.90 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:35<00:00,  4.78it/s]7s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:40<00:00,  9.78it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.29it/s]
Epoch-9, seen -- Mean err: 6.71, Acc: 0.95, Rec : 0.97, Class and Pose  : 0.94
Validation time for epoch 9: 2.33 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:49<00:00, 13.86it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.43it/s]
Epoch-9, unseen -- Mean err: 10.14, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 9: 6.10 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:16<00:00, 13.50it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:07<00:00,  5.30it/s]
Epoch-9, seen_occ -- Mean err: 23.96, Acc: 0.62, Rec : 0.79, Class and Pose  : 0.61
Validation time for epoch 9: 5.53 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:45<00:00, 14.33it/s]
100%|█████████████████████████████████████████████| 623/623 [31:03<00:00,  2.99s/it]
Epoch-9, unseen_occ -- Mean err: 24.45, Acc: 0.64, Rec : 0.90, Class and Pose  : 0.64
Validation time for epoch 9: 2.93 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:34<00:00,  5.00it/s]7s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:38<00:00,  9.93it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.10it/s]
Epoch-10, seen -- Mean err: 7.00, Acc: 0.94, Rec : 0.96, Class and Pose  : 0.94
Validation time for epoch 10: 2.27 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:53<00:00, 13.70it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.03it/s]
Epoch-10, unseen -- Mean err: 10.58, Acc: 0.89, Rec : 0.97, Class and Pose  : 0.89
Validation time for epoch 10: 6.17 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:12<00:00, 13.67it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.46it/s]
Epoch-10, seen_occ -- Mean err: 22.95, Acc: 0.63, Rec : 0.78, Class and Pose  : 0.61
Validation time for epoch 10: 5.48 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:46<00:00, 14.25it/s]
100%|█████████████████████████████████████████████| 623/623 [30:48<00:00,  2.97s/it]
Epoch-10, unseen_occ -- Mean err: 23.19, Acc: 0.67, Rec : 0.88, Class and Pose  : 0.66
Validation time for epoch 10: 2.96 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:35<00:00,  4.81it/s]4s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:34<00:00, 10.38it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.34it/s]
Epoch-11, seen -- Mean err: 6.63, Acc: 0.95, Rec : 0.97, Class and Pose  : 0.95
Validation time for epoch 11: 2.23 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:51<00:00, 13.77it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:13<00:00,  5.83it/s]
Epoch-11, unseen -- Mean err: 10.54, Acc: 0.89, Rec : 0.96, Class and Pose  : 0.89
Validation time for epoch 11: 6.14 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:17<00:00, 13.46it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.49it/s]
Epoch-11, seen_occ -- Mean err: 22.36, Acc: 0.65, Rec : 0.79, Class and Pose  : 0.63
Validation time for epoch 11: 5.58 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:44<00:00, 14.45it/s]
100%|█████████████████████████████████████████████| 623/623 [30:45<00:00,  2.96s/it]
Epoch-11, unseen_occ -- Mean err: 23.92, Acc: 0.67, Rec : 0.88, Class and Pose  : 0.66
Validation time for epoch 11: 2.90 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:35<00:00,  4.83it/s]9s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:37<00:00, 10.09it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.10it/s]
Epoch-12, seen -- Mean err: 7.06, Acc: 0.95, Rec : 0.96, Class and Pose  : 0.94
Validation time for epoch 12: 2.26 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:53<00:00, 13.73it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.08it/s]
Epoch-12, unseen -- Mean err: 9.83, Acc: 0.89, Rec : 0.97, Class and Pose  : 0.89
Validation time for epoch 12: 6.15 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:15<00:00, 13.53it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:07<00:00,  5.24it/s]
Epoch-12, seen_occ -- Mean err: 22.09, Acc: 0.65, Rec : 0.79, Class and Pose  : 0.63
Validation time for epoch 12: 5.55 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:50<00:00, 13.92it/s]
100%|█████████████████████████████████████████████| 623/623 [30:37<00:00,  2.95s/it]
Epoch-12, unseen_occ -- Mean err: 24.37, Acc: 0.65, Rec : 0.87, Class and Pose  : 0.64
Validation time for epoch 12: 3.02 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:34<00:00,  4.88it/s]4s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:37<00:00, 10.07it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.15it/s]
Epoch-13, seen -- Mean err: 6.58, Acc: 0.95, Rec : 0.97, Class and Pose  : 0.95
Validation time for epoch 13: 2.27 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [06:05<00:00, 13.25it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.37it/s]
Epoch-13, unseen -- Mean err: 9.45, Acc: 0.91, Rec : 0.98, Class and Pose  : 0.91
Validation time for epoch 13: 6.39 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:19<00:00, 13.37it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.88it/s]
Epoch-13, seen_occ -- Mean err: 21.92, Acc: 0.65, Rec : 0.80, Class and Pose  : 0.64
Validation time for epoch 13: 5.60 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:48<00:00, 14.12it/s]
100%|█████████████████████████████████████████████| 623/623 [30:59<00:00,  2.99s/it]
Epoch-13, unseen_occ -- Mean err: 25.84, Acc: 0.65, Rec : 0.91, Class and Pose  : 0.65
Validation time for epoch 13: 2.99 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.68it/s]2s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:31<00:00, 10.77it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:12<00:00,  6.11it/s]
Epoch-14, seen -- Mean err: 6.72, Acc: 0.96, Rec : 0.97, Class and Pose  : 0.95
Validation time for epoch 14: 2.18 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:57<00:00, 13.55it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.56it/s]
Epoch-14, unseen -- Mean err: 9.10, Acc: 0.91, Rec : 0.98, Class and Pose  : 0.91
Validation time for epoch 14: 6.23 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:17<00:00, 13.46it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:07<00:00,  5.40it/s]
Epoch-14, seen_occ -- Mean err: 22.04, Acc: 0.66, Rec : 0.80, Class and Pose  : 0.64
Validation time for epoch 14: 5.54 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:45<00:00, 14.38it/s]
100%|█████████████████████████████████████████████| 623/623 [30:50<00:00,  2.97s/it]
Epoch-14, unseen_occ -- Mean err: 23.54, Acc: 0.68, Rec : 0.91, Class and Pose  : 0.67
Validation time for epoch 14: 2.95 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:35<00:00,  4.83it/s]4s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:34<00:00, 10.33it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:11<00:00,  6.38it/s]
Epoch-15, seen -- Mean err: 6.70, Acc: 0.96, Rec : 0.96, Class and Pose  : 0.95
Validation time for epoch 15: 2.24 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:57<00:00, 13.56it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:13<00:00,  5.62it/s]
Epoch-15, unseen -- Mean err: 9.23, Acc: 0.91, Rec : 0.98, Class and Pose  : 0.91
Validation time for epoch 15: 6.22 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [05:15<00:00, 13.52it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.71it/s]
Epoch-15, seen_occ -- Mean err: 21.55, Acc: 0.66, Rec : 0.80, Class and Pose  : 0.65
Validation time for epoch 15: 5.54 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:41<00:00, 14.69it/s]
100%|█████████████████████████████████████████████| 623/623 [30:45<00:00,  2.96s/it]
Epoch-15, unseen_occ -- Mean err: 23.00, Acc: 0.69, Rec : 0.90, Class and Pose  : 0.68
Validation time for epoch 15: 2.88 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:36<00:00,  4.62it/s]3s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:16<00:00, 12.76it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:10<00:00,  7.45it/s]
Epoch-16, seen -- Mean err: 6.66, Acc: 0.96, Rec : 0.97, Class and Pose  : 0.96
Validation time for epoch 16: 1.97 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:19<00:00, 15.18it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:10<00:00,  7.11it/s]
Epoch-16, unseen -- Mean err: 8.95, Acc: 0.91, Rec : 0.98, Class and Pose  : 0.91
Validation time for epoch 16: 5.54 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:48<00:00, 14.83it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.77it/s]
Epoch-16, seen_occ -- Mean err: 21.70, Acc: 0.66, Rec : 0.80, Class and Pose  : 0.64
Validation time for epoch 16: 5.03 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:35<00:00, 15.32it/s]
100%|█████████████████████████████████████████████| 623/623 [20:24<00:00,  1.97s/it]
Epoch-16, unseen_occ -- Mean err: 23.36, Acc: 0.68, Rec : 0.89, Class and Pose  : 0.67
Validation time for epoch 16: 2.75 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [00:22<00:00,  7.57it/s]9s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████| 981/981 [01:07<00:00, 14.52it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:09<00:00,  7.71it/s]
Epoch-17, seen -- Mean err: 6.68, Acc: 0.96, Rec : 0.96, Class and Pose  : 0.96
Validation time for epoch 17: 1.56 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4848/4848 [05:32<00:00, 14.57it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:10<00:00,  7.02it/s]
Epoch-17, unseen -- Mean err: 9.19, Acc: 0.91, Rec : 0.99, Class and Pose  : 0.91
Validation time for epoch 17: 5.76 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [04:43<00:00, 15.09it/s]
处理模板数据: 100%|█████████████████████████████████| 38/38 [00:06<00:00,  5.64it/s]
Epoch-17, seen_occ -- Mean err: 21.74, Acc: 0.65, Rec : 0.80, Class and Pose  : 0.64
Validation time for epoch 17: 4.97 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████| 2377/2377 [02:31<00:00, 15.70it/s]
 68%|██████████████████████████████▋              | 424/623 [13:51<06:30,  1.96s/it]
Epoch-17, unseen_occ -- Mean err: 24.35, Acc: 0.67, Rec : 0.89, Class and Pose  : 0.66
Validation time for epoch 17: 2.68 minutes
 72%|████████████████████████████▊           | 18/25 [14:22:20<5:35:21, 2874.48s/it]
Traceback (most recent call last):
  File "train_new.py", line 433, in <module>
    main()
  File "train_new.py", line 318, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 147, in train
    neg_features = model(neg)  # 返回字典
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 258, in forward
    cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/new_dino_network.py", line 57, in forward
    _ = self.model(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 325, in forward
    ret = self.forward_features(*args, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 261, in forward_features
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 254, in forward
    return super().forward(x_or_x_list)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 112, in forward
    x = x + attn_residual_func(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 91, in attn_residual_func
    return self.ls1(self.attn(self.norm1(x)))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/attention.py", line 77, in forward
    return super().forward(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/attention.py", line 66, in forward
    x = (attn @ v).transpose(1, 2).reshape(B, N, C)
KeyboardInterrupt
