{"time":"2025-07-25T22:55:13.083700727+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250725_225513-xqzfy4hw/logs/debug-core.log"}
{"time":"2025-07-25T22:55:13.323936298+08:00","level":"INFO","msg":"created new stream","id":"xqzfy4hw"}
{"time":"2025-07-25T22:55:13.324003834+08:00","level":"INFO","msg":"stream: started","id":"xqzfy4hw"}
{"time":"2025-07-25T22:55:13.324048339+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"xqzfy4hw"}
{"time":"2025-07-25T22:55:13.324299009+08:00","level":"INFO","msg":"handler: started","stream_id":"xqzfy4hw"}
{"time":"2025-07-25T22:55:13.324329717+08:00","level":"INFO","msg":"sender: started","stream_id":"xqzfy4hw"}
{"time":"2025-07-25T22:55:14.055956013+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-25T23:00:50.019880739+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-25T23:18:20.017850339+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-25T23:38:50.017646593+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T00:24:50.016341597+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T00:24:57.241515792+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T00:26:05.024834183+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T00:26:20.01745884+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T00:43:50.016547587+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T01:40:35.021575703+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T02:11:20.017806256+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T02:11:27.301545841+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T02:11:36.845523863+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T02:11:50.187902688+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T02:12:12.514716271+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T02:12:12.880321814+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": local error: tls: bad record MAC"}
{"time":"2025-07-26T02:12:20.349237984+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-07-26T02:12:30.269062501+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-07-26T02:19:27.366995667+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T02:20:27.367357524+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T03:25:07.566688628+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": local error: tls: bad record MAC"}
{"time":"2025-07-26T06:45:45.594172114+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T13:01:00.594441134+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/xqzfy4hw/file_stream\": EOF"}
{"time":"2025-07-26T13:17:37.190051959+08:00","level":"INFO","msg":"stream: closing","id":"xqzfy4hw"}
{"time":"2025-07-26T13:17:37.190093688+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-26T13:17:37.194871664+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-26T13:17:38.383637193+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-26T13:17:39.562300757+08:00","level":"INFO","msg":"handler: closed","stream_id":"xqzfy4hw"}
{"time":"2025-07-26T13:17:39.562329289+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"xqzfy4hw"}
{"time":"2025-07-26T13:17:39.562344291+08:00","level":"INFO","msg":"sender: closed","stream_id":"xqzfy4hw"}
{"time":"2025-07-26T13:17:39.562416284+08:00","level":"INFO","msg":"stream: closed","id":"xqzfy4hw"}
