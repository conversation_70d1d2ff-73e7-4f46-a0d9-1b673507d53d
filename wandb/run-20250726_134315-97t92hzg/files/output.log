特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.14M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 1开始
   📊 Hash阶段优化器参数数量: 0.14M
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:43<00:00,  2.86s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:36<00:00,  4.62it/s]5s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:20<00:00, 12.22it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.30it/s]
Epoch-0, seen -- Mean err: 4.55, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 2.00 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:40<00:00, 14.25it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.39it/s]
Epoch-0, unseen -- Mean err: 4.61, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 5.93 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:04<00:00, 14.02it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.25it/s]
Epoch-0, seen_occ -- Mean err: 12.92, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.85
Validation time for epoch 0: 5.33 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:35<00:00, 15.32it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:58<00:00,  2.98s/it]
Epoch-0, unseen_occ -- Mean err: 9.27, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 0: 2.75 minutes

🔄 Epoch 1: 切换到Hash联合训练阶段
   🔓 训练所有参数：主要模型 + Hash编码器
   ✅ 真正的端到端联合训练
   📊 联合训练参数统计:
      - 主要模型: 1.43M
      - Hash编码器: 0.14M
      - 总计: 1.56M
   📈 联合训练学习率: 0.0001
   ✅ 优化器已切换到联合训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:36<00:00,  4.64it/s]3s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:31<00:00, 10.70it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.03it/s]
Epoch-1, seen -- Mean err: 8.85, Acc: 0.91, Rec : 0.94, Class and Pose  : 0.90
Validation time for epoch 1: 2.20 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:45<00:00, 14.01it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.02it/s]
Epoch-1, unseen -- Mean err: 11.75, Acc: 0.86, Rec : 0.95, Class and Pose  : 0.86
Validation time for epoch 1: 6.03 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:59<00:00, 14.27it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.75it/s]
Epoch-1, seen_occ -- Mean err: 21.39, Acc: 0.65, Rec : 0.79, Class and Pose  : 0.64
Validation time for epoch 1: 5.27 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:44<00:00, 14.49it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [31:08<00:00,  3.00s/it]
Epoch-1, unseen_occ -- Mean err: 24.43, Acc: 0.63, Rec : 0.88, Class and Pose  : 0.63
Validation time for epoch 1: 2.90 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.79it/s]7s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:30<00:00, 10.86it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.93it/s]
Epoch-2, seen -- Mean err: 8.83, Acc: 0.91, Rec : 0.95, Class and Pose  : 0.90
Validation time for epoch 2: 2.15 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:45<00:00, 14.02it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.20it/s]
Epoch-2, unseen -- Mean err: 10.25, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 2: 6.03 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:07<00:00, 13.91it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.52it/s]
Epoch-2, seen_occ -- Mean err: 22.23, Acc: 0.65, Rec : 0.80, Class and Pose  : 0.63
Validation time for epoch 2: 5.39 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:44<00:00, 14.48it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [31:04<00:00,  2.99s/it]
Epoch-2, unseen_occ -- Mean err: 25.24, Acc: 0.60, Rec : 0.89, Class and Pose  : 0.59
Validation time for epoch 2: 2.90 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.74it/s]8s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:28<00:00, 11.11it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.37it/s]
Epoch-3, seen -- Mean err: 8.28, Acc: 0.92, Rec : 0.96, Class and Pose  : 0.91
Validation time for epoch 3: 2.13 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:41<00:00, 14.20it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.13it/s]
Epoch-3, unseen -- Mean err: 10.74, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 3: 5.95 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:07<00:00, 13.88it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.96it/s]
Epoch-3, seen_occ -- Mean err: 24.92, Acc: 0.60, Rec : 0.80, Class and Pose  : 0.59
Validation time for epoch 3: 5.39 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:49<00:00, 14.02it/s]
  0%|                                                                                                                                           | 0/623 [00:14<?, ?it/s]
Epoch-3, unseen_occ -- Mean err: 24.17, Acc: 0.63, Rec : 0.89, Class and Pose  : 0.63
Validation time for epoch 3: 3.00 minutes
 16%|████████████████████                                                                                                         | 4/25 [3:09:09<16:33:04, 2837.34s/it]
Traceback (most recent call last):
  File "train_new.py", line 433, in <module>
    main()
  File "train_new.py", line 318, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 58, in train
    for i, miniBatch in enumerate(tqdm(train_loader)):
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/std.py", line 1181, in __iter__
    for obj in iterable:
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 681, in __next__
    data = self._next_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1359, in _next_data
    idx, data = self._get_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1315, in _get_data
    success, data = self._try_get_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1163, in _try_get_data
    data = self._data_queue.get(timeout=timeout)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/queue.py", line 179, in get
    self.not_empty.wait(remaining)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/threading.py", line 306, in wait
    gotit = waiter.acquire(True, timeout)
KeyboardInterrupt
