特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:44<00:00,  1.80s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:22<00:00,  7.61it/s]0s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:07<00:00, 14.43it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.41it/s]
Epoch-0, seen -- Mean err: 4.52, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 1.56 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:13<00:00, 15.46it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.35it/s]
Epoch-0, unseen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 5.45 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:33<00:00, 15.61it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.97it/s]
Epoch-0, seen_occ -- Mean err: 12.58, Acc: 0.86, Rec : 0.93, Class and Pose  : 0.85
Validation time for epoch 0: 4.81 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:25<00:00, 16.36it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:53<00:00,  1.82s/it]
Epoch-0, unseen_occ -- Mean err: 8.90, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 0: 2.60 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:22<00:00,  7.62it/s]6s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:11<00:00, 13.68it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.63it/s]
Epoch-1, seen -- Mean err: 4.36, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 1: 1.66 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:20<00:00, 15.11it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.42it/s]
Epoch-1, unseen -- Mean err: 4.58, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 5.60 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:36<00:00, 15.47it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.20it/s]
Epoch-1, seen_occ -- Mean err: 13.43, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 1: 4.85 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:23<00:00, 16.53it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:50<00:00,  1.81s/it]
Epoch-1, unseen_occ -- Mean err: 10.87, Acc: 0.88, Rec : 0.97, Class and Pose  : 0.88
Validation time for epoch 1: 2.55 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:22<00:00,  7.69it/s]0s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:05<00:00, 15.01it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.04it/s]
Epoch-2, seen -- Mean err: 4.26, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 2: 1.53 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:18<00:00, 15.24it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.05it/s]
Epoch-2, unseen -- Mean err: 4.65, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 5.55 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:34<00:00, 15.55it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.17it/s]
Epoch-2, seen_occ -- Mean err: 13.96, Acc: 0.83, Rec : 0.90, Class and Pose  : 0.82
Validation time for epoch 2: 4.83 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:31<00:00, 15.64it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:39<00:00,  1.80s/it]
Epoch-2, unseen_occ -- Mean err: 12.11, Acc: 0.85, Rec : 0.97, Class and Pose  : 0.85
Validation time for epoch 2: 2.70 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:21<00:00,  7.88it/s]5s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:10<00:00, 13.84it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.54it/s]
Epoch-3, seen -- Mean err: 4.06, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 3: 1.60 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:16<00:00, 15.32it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.59it/s]
Epoch-3, unseen -- Mean err: 4.65, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 5.53 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:40<00:00, 15.24it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.16it/s]
Epoch-3, seen_occ -- Mean err: 12.80, Acc: 0.85, Rec : 0.91, Class and Pose  : 0.84
Validation time for epoch 3: 4.89 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:23<00:00, 16.60it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:57<00:00,  1.83s/it]
Epoch-3, unseen_occ -- Mean err: 10.70, Acc: 0.87, Rec : 0.97, Class and Pose  : 0.86
Validation time for epoch 3: 2.58 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:24<00:00,  7.04it/s]0s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:10<00:00, 13.91it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.44it/s]
Epoch-4, seen -- Mean err: 4.10, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 4: 1.63 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:24<00:00, 14.93it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.09it/s]
Epoch-4, unseen -- Mean err: 4.63, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 5.66 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:26<00:00, 16.04it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.28it/s]
Epoch-4, seen_occ -- Mean err: 12.79, Acc: 0.85, Rec : 0.91, Class and Pose  : 0.85
Validation time for epoch 4: 4.68 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:26<00:00, 16.25it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:35<00:00,  1.79s/it]
Epoch-4, unseen_occ -- Mean err: 10.36, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 4: 2.59 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:22<00:00,  7.41it/s]8s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像:  57%|█████████████████████████████████████████████████████████████████▊                                                 | 561/981 [00:41<00:30, 13.58it/s]
 20%|█████████████████████████                                                                                                    | 5/25 [3:07:28<12:29:53, 2249.67s/it]
Traceback (most recent call last):
  File "train_new.py", line 433, in <module>
    main()
  File "train_new.py", line 353, in main
    testing_score = testing_utils_dino.test(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 154, in test
    original_query_features = model(query)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 258, in forward
    cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/new_dino_network.py", line 57, in forward
    _ = self.model(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 325, in forward
    ret = self.forward_features(*args, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 261, in forward_features
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 254, in forward
    return super().forward(x_or_x_list)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 112, in forward
    x = x + attn_residual_func(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 91, in attn_residual_func
    return self.ls1(self.attn(self.norm1(x)))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/attention.py", line 77, in forward
    return super().forward(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/attention.py", line 67, in forward
    x = self.proj(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/linear.py", line 114, in forward
    return F.linear(input, self.weight, self.bias)
KeyboardInterrupt
