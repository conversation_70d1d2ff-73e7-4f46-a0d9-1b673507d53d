特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.14M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 1开始
   📊 Hash阶段优化器参数数量: 0.14M
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:53<00:00,  2.88s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.82it/s]9s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:44<00:00,  9.40it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.24it/s]
Epoch-0, seen -- Mean err: 4.55, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 2.38 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:54<00:00, 13.67it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:13<00:00,  5.78it/s]
Epoch-0, unseen -- Mean err: 4.61, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 6.19 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:59<00:00, 14.27it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.88it/s]
Epoch-0, seen_occ -- Mean err: 12.92, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.85
Validation time for epoch 0: 5.27 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:33<00:00, 15.52it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:45<00:00,  2.96s/it]
Epoch-0, unseen_occ -- Mean err: 9.27, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 0: 2.72 minutes

🔄 Epoch 1: 切换到Hash联合训练阶段
   🔓 训练所有参数：主要模型 + Hash编码器
   ✅ 真正的端到端联合训练
   📊 联合训练参数统计:
      - 主要模型: 1.43M
      - Hash编码器: 0.14M
      - 总计: 1.56M
   📈 联合训练学习率: 0.0001
   ✅ 优化器已切换到联合训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.81it/s]0s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:40<00:00,  9.78it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:17<00:00,  4.40it/s]
Epoch-1, seen -- Mean err: 7.46, Acc: 0.94, Rec : 0.96, Class and Pose  : 0.93
Validation time for epoch 1: 2.33 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:07<00:00, 13.19it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.36it/s]
Epoch-1, unseen -- Mean err: 9.76, Acc: 0.90, Rec : 0.96, Class and Pose  : 0.90
Validation time for epoch 1: 6.47 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:03<00:00, 14.06it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.49it/s]
Epoch-1, seen_occ -- Mean err: 21.10, Acc: 0.67, Rec : 0.77, Class and Pose  : 0.65
Validation time for epoch 1: 5.33 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:43<00:00, 14.53it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:12<00:00,  2.91s/it]
Epoch-1, unseen_occ -- Mean err: 20.61, Acc: 0.71, Rec : 0.91, Class and Pose  : 0.71
Validation time for epoch 1: 2.90 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:36<00:00,  4.62it/s]1s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像:  51%|██████████████████████████████████████████████████████████▍                                                        | 499/981 [00:52<00:51,  9.45it/s]
  8%|██████████                                                                                                                   | 2/25 [2:06:22<24:13:17, 3791.21s/it]
Traceback (most recent call last):
  File "train_new.py", line 433, in <module>
    main()
  File "train_new.py", line 353, in main
    testing_score = testing_utils_dino.test(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 236, in test
    err, acc, class_score, class_and_pose = calculate_score(pred_location=pred_pose,
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 14, in calculate_score
    unique_ids, inverse_indices = torch.unique(id_obj, sorted=True, return_inverse=True)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/_jit_internal.py", line 421, in fn
    return if_true(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/_jit_internal.py", line 423, in fn
    return if_false(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/functional.py", line 861, in _return_inverse
    output, inverse_indices, _ = _unique_impl(input, sorted, return_inverse, return_counts, dim)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/functional.py", line 765, in _unique_impl
    output, inverse_indices, counts = torch._unique2(
KeyboardInterrupt
