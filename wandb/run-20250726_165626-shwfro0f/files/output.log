特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.14M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.14M
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [31:39<00:00,  3.05s/it]

🔄 Epoch 0: 切换到Hash联合训练阶段
   🔓 训练所有参数：主要模型 + Hash编码器
   ✅ 真正的端到端联合训练
   📊 联合训练参数统计:
      - 主要模型: 1.43M
      - Hash编码器: 0.14M
      - 总计: 1.56M
   📈 联合训练学习率: 0.0001
   ✅ 优化器已切换到联合训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.71it/s]7s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:20<00:00, 12.22it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.35it/s]
Epoch-0, seen -- Mean err: 4.67, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 1.83 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:54<00:00, 13.67it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:13<00:00,  5.76it/s]
Epoch-0, unseen -- Mean err: 4.74, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 6.18 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:14<00:00, 13.58it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.86it/s]
Epoch-0, seen_occ -- Mean err: 13.30, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 0: 5.53 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:26<00:00, 11.53it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:52<00:00,  2.97s/it]
Epoch-0, unseen_occ -- Mean err: 10.72, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 0: 3.60 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:24<00:00,  6.92it/s]4s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:21<00:00, 12.08it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.99it/s]
Epoch-1, seen -- Mean err: 6.20, Acc: 0.96, Rec : 0.98, Class and Pose  : 0.96
Validation time for epoch 1: 1.81 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:05<00:00, 13.26it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.95it/s]
Epoch-1, unseen -- Mean err: 8.23, Acc: 0.93, Rec : 0.98, Class and Pose  : 0.93
Validation time for epoch 1: 6.36 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:17<00:00, 13.45it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.80it/s]
Epoch-1, seen_occ -- Mean err: 19.09, Acc: 0.72, Rec : 0.82, Class and Pose  : 0.71
Validation time for epoch 1: 5.55 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:43<00:00, 10.63it/s]
 89%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████▏             | 556/623 [28:01<03:22,  3.02s/it]
Epoch-1, unseen_occ -- Mean err: 18.02, Acc: 0.75, Rec : 0.93, Class and Pose  : 0.74
Validation time for epoch 1: 3.89 minutes
  8%|██████████                                                                                                                   | 2/25 [2:05:37<24:04:42, 3768.80s/it]
Traceback (most recent call last):
  File "train_new.py", line 433, in <module>
    main()
  File "train_new.py", line 318, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 466, in train
    loss_dict['total_loss'].backward()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/_tensor.py", line 396, in backward
    torch.autograd.backward(self, gradient, retain_graph, create_graph, inputs=inputs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/autograd/__init__.py", line 173, in backward
    Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
KeyboardInterrupt
