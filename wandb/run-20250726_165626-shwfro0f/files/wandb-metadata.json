{"os": "Linux-5.4.0-214-generic-x86_64-with-glibc2.17", "python": "CPython 3.8.18", "startedAt": "2025-07-26T08:56:26.304526Z", "args": ["--config_path", "config_run/LM_DINO_split1.json", "--use_wandb", "--wandb_name", "726-1 from 0"], "program": "train_new.py", "codePath": "train_new.py", "git": {"remote": "https://github.com/jialeren/my-diff-feats-pose.git", "commit": "9978404d00177beebfa2e402a0dbbf69ec5efde0"}, "email": "<EMAIL>", "root": "/home/<USER>/projects/RJL2025/my-diff-feats-pose", "host": "dbcloud", "executable": "/home/<USER>/anaconda3/envs/diff-feats/bin/python", "codePathLocal": "train_new.py", "cpu_count": 32, "cpu_count_logical": 64, "gpu": "NVIDIA GeForce RTX 4090", "gpu_count": 8, "disk": {"/": {"total": "2013991550976", "used": "1459478605824"}}, "memory": {"total": "1081839955968"}, "cpu": {"count": 32, "countLogical": 64}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}, {"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}, {"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}, {"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}, {"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}, {"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}, {"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}, {"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}], "cudaVersion": "12.4"}