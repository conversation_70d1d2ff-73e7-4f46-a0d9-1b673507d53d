特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.14M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.14M
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [32:04<00:00,  3.09s/it]

🔄 Epoch 0: 切换到Hash联合训练阶段
   🔓 训练所有参数：主要模型 + Hash编码器
   ✅ 使用分离学习率的联合训练
   📊 联合训练参数统计:
      - 主要模型: 1.43M (lr: 1e-05)
      - Hash编码器: 0.14M (lr: 0.0001)
      - 总计: 1.56M
   ✅ 优化器已切换到分离学习率联合训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.57it/s]1s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:23<00:00, 11.80it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.35it/s]
Epoch-0, seen -- Mean err: 4.67, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 1.89 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:13<00:00, 12.99it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.05it/s]
Epoch-0, unseen -- Mean err: 4.74, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 6.49 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:26<00:00, 13.08it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.61it/s]
Epoch-0, seen_occ -- Mean err: 13.30, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 0: 5.72 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:16<00:00, 12.08it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [31:23<00:00,  3.02s/it]
Epoch-0, unseen_occ -- Mean err: 10.72, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 0: 3.47 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:26<00:00,  6.47it/s]6s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:23<00:00, 11.79it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.97it/s]
Epoch-1, seen -- Mean err: 6.20, Acc: 0.96, Rec : 0.98, Class and Pose  : 0.96
Validation time for epoch 1: 1.90 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:09<00:00, 13.11it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.46it/s]
Epoch-1, unseen -- Mean err: 8.23, Acc: 0.93, Rec : 0.98, Class and Pose  : 0.93
Validation time for epoch 1: 6.46 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:18<00:00, 13.40it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.20it/s]
Epoch-1, seen_occ -- Mean err: 19.09, Acc: 0.72, Rec : 0.82, Class and Pose  : 0.71
Validation time for epoch 1: 5.57 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:12<00:00, 12.33it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [31:06<00:00,  3.00s/it]
Epoch-1, unseen_occ -- Mean err: 18.02, Acc: 0.75, Rec : 0.93, Class and Pose  : 0.74
Validation time for epoch 1: 3.40 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:26<00:00,  6.35it/s]4s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:19<00:00, 12.40it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.46it/s]
Epoch-2, seen -- Mean err: 6.94, Acc: 0.96, Rec : 0.98, Class and Pose  : 0.95
Validation time for epoch 2: 1.81 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:07<00:00, 13.18it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.21it/s]
Epoch-2, unseen -- Mean err: 9.26, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 2: 6.39 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:21<00:00, 13.27it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.10it/s]
Epoch-2, seen_occ -- Mean err: 19.87, Acc: 0.71, Rec : 0.82, Class and Pose  : 0.70
Validation time for epoch 2: 5.64 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:30<00:00, 11.30it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:59<00:00,  2.98s/it]
Epoch-2, unseen_occ -- Mean err: 22.09, Acc: 0.64, Rec : 0.92, Class and Pose  : 0.64
Validation time for epoch 2: 3.68 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:26<00:00,  6.45it/s]0s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:23<00:00, 11.81it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.20it/s]
Epoch-3, seen -- Mean err: 7.51, Acc: 0.93, Rec : 0.96, Class and Pose  : 0.93
Validation time for epoch 3: 1.91 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:08<00:00, 13.16it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.14it/s]
Epoch-3, unseen -- Mean err: 9.55, Acc: 0.90, Rec : 0.97, Class and Pose  : 0.89
Validation time for epoch 3: 6.40 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:25<00:00, 13.11it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  6.40it/s]
Epoch-3, seen_occ -- Mean err: 19.99, Acc: 0.68, Rec : 0.82, Class and Pose  : 0.67
Validation time for epoch 3: 5.70 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:43<00:00, 10.61it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:59<00:00,  2.99s/it]
Epoch-3, unseen_occ -- Mean err: 21.17, Acc: 0.67, Rec : 0.91, Class and Pose  : 0.67
Validation time for epoch 3: 3.89 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.60it/s]3s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:18<00:00, 12.51it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.94it/s]
Epoch-4, seen -- Mean err: 10.35, Acc: 0.88, Rec : 0.92, Class and Pose  : 0.87
Validation time for epoch 4: 1.82 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:12<00:00, 13.02it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.04it/s]
Epoch-4, unseen -- Mean err: 11.41, Acc: 0.84, Rec : 0.96, Class and Pose  : 0.84
Validation time for epoch 4: 6.50 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:22<00:00, 13.26it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:08<00:00,  4.62it/s]
Epoch-4, seen_occ -- Mean err: 19.75, Acc: 0.67, Rec : 0.83, Class and Pose  : 0.66
Validation time for epoch 4: 5.63 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:42<00:00, 10.67it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [29:54<00:00,  2.88s/it]
Epoch-4, unseen_occ -- Mean err: 23.39, Acc: 0.59, Rec : 0.93, Class and Pose  : 0.59
Validation time for epoch 4: 3.92 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:22<00:00,  7.47it/s]5s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:13<00:00, 13.26it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.73it/s]
Epoch-5, seen -- Mean err: 8.34, Acc: 0.92, Rec : 0.95, Class and Pose  : 0.92
Validation time for epoch 5: 1.68 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:26<00:00, 14.87it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  6.96it/s]
Epoch-5, unseen -- Mean err: 11.32, Acc: 0.86, Rec : 0.94, Class and Pose  : 0.86
Validation time for epoch 5: 5.70 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:53<00:00, 14.57it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  6.35it/s]
Epoch-5, seen_occ -- Mean err: 21.43, Acc: 0.65, Rec : 0.78, Class and Pose  : 0.63
Validation time for epoch 5: 5.13 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:39<00:00, 14.94it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [20:40<00:00,  1.99s/it]
Epoch-5, unseen_occ -- Mean err: 24.01, Acc: 0.64, Rec : 0.93, Class and Pose  : 0.63
Validation time for epoch 5: 2.81 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:22<00:00,  7.56it/s]8s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:15<00:00, 12.93it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  6.97it/s]
Epoch-6, seen -- Mean err: 8.78, Acc: 0.92, Rec : 0.95, Class and Pose  : 0.91
Validation time for epoch 6: 1.70 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:27<00:00, 14.79it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  6.93it/s]
Epoch-6, unseen -- Mean err: 12.18, Acc: 0.84, Rec : 0.96, Class and Pose  : 0.84
Validation time for epoch 6: 5.71 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:44<00:00, 15.01it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  6.39it/s]
Epoch-6, seen_occ -- Mean err: 23.94, Acc: 0.60, Rec : 0.76, Class and Pose  : 0.59
Validation time for epoch 6: 4.98 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:29<00:00, 15.92it/s]
 16%|████████████████████▍                                                                                                             | 98/623 [03:27<18:31,  2.12s/it]
Epoch-6, unseen_occ -- Mean err: 27.38, Acc: 0.60, Rec : 0.88, Class and Pose  : 0.60
Validation time for epoch 6: 2.65 minutes
 28%|███████████████████████████████████                                                                                          | 7/25 [5:30:18<14:09:22, 2831.23s/it]
Traceback (most recent call last):
  File "train_new.py", line 453, in <module>
  File "train_new.py", line 338, in main
    if (epoch + 1) % 1 == 0:
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 148, in train
    neg_features = model(neg)  # 返回字典
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 258, in forward
    cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/new_dino_network.py", line 57, in forward
    _ = self.model(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 325, in forward
    ret = self.forward_features(*args, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 261, in forward_features
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1148, in _call_impl
    result = forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 254, in forward
    return super().forward(x_or_x_list)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 112, in forward
    x = x + attn_residual_func(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 91, in attn_residual_func
    return self.ls1(self.attn(self.norm1(x)))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/layer_scale.py", line 27, in forward
    return x.mul_(self.gamma) if self.inplace else x * self.gamma
KeyboardInterrupt
