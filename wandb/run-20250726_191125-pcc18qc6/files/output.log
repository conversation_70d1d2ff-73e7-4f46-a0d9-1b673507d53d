特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.14M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 1开始
   📊 Hash阶段优化器参数数量: 0.14M
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:08<00:00,  2.90s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:36<00:00,  4.67it/s]2s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:38<00:00,  9.94it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.05it/s]
Epoch-0, seen -- Mean err: 4.55, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 2.31 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:49<00:00, 13.87it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.44it/s]
Epoch-0, unseen -- Mean err: 4.61, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 6.09 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:08<00:00, 13.86it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.47it/s]
Epoch-0, seen_occ -- Mean err: 12.92, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.85
Validation time for epoch 0: 5.39 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:43<00:00, 14.54it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:54<00:00,  2.98s/it]
Epoch-0, unseen_occ -- Mean err: 9.27, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 0: 2.91 minutes

🔄 Epoch 1: 切换到Hash联合训练阶段
   🔓 训练所有参数：主要模型 + Hash编码器
   ✅ 使用分离学习率的联合训练
   📊 联合训练参数统计:
      - 主要模型: 1.43M (lr: 1e-05)
      - Hash编码器: 0.14M (lr: 0.0001)
      - 总计: 1.56M
   ✅ 优化器已切换到分离学习率联合训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:37<00:00,  4.50it/s]9s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:48<00:00,  9.05it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:17<00:00,  4.27it/s]
Epoch-1, seen -- Mean err: 7.46, Acc: 0.94, Rec : 0.96, Class and Pose  : 0.93
Validation time for epoch 1: 2.52 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:10<00:00, 13.08it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.86it/s]
Epoch-1, unseen -- Mean err: 9.76, Acc: 0.90, Rec : 0.96, Class and Pose  : 0.90
Validation time for epoch 1: 6.54 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:21<00:00, 13.28it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.14it/s]
Epoch-1, seen_occ -- Mean err: 21.10, Acc: 0.67, Rec : 0.77, Class and Pose  : 0.65
Validation time for epoch 1: 5.63 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:38<00:00, 15.01it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:54<00:00,  2.98s/it]
Epoch-1, unseen_occ -- Mean err: 20.61, Acc: 0.71, Rec : 0.91, Class and Pose  : 0.71
Validation time for epoch 1: 2.84 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:36<00:00,  4.67it/s]3s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:28<00:00, 11.04it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:16<00:00,  4.54it/s]
Epoch-2, seen -- Mean err: 6.97, Acc: 0.95, Rec : 0.98, Class and Pose  : 0.95
Validation time for epoch 2: 2.15 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:59<00:00, 13.50it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.85it/s]
Epoch-2, unseen -- Mean err: 8.79, Acc: 0.90, Rec : 0.99, Class and Pose  : 0.90
Validation time for epoch 2: 6.31 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:24<00:00, 13.17it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  4.90it/s]
Epoch-2, seen_occ -- Mean err: 21.45, Acc: 0.67, Rec : 0.81, Class and Pose  : 0.66
Validation time for epoch 2: 5.68 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:44<00:00, 14.45it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:42<00:00,  2.96s/it]
Epoch-2, unseen_occ -- Mean err: 23.40, Acc: 0.65, Rec : 0.92, Class and Pose  : 0.65
Validation time for epoch 2: 2.96 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:35<00:00,  4.73it/s]4s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:43<00:00,  9.44it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:17<00:00,  4.35it/s]
Epoch-3, seen -- Mean err: 7.52, Acc: 0.93, Rec : 0.96, Class and Pose  : 0.93
Validation time for epoch 3: 2.41 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:23<00:00, 12.65it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.09it/s]
Epoch-3, unseen -- Mean err: 10.83, Acc: 0.88, Rec : 0.97, Class and Pose  : 0.88
Validation time for epoch 3: 6.74 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:17<00:00, 13.44it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.58it/s]
Epoch-3, seen_occ -- Mean err: 21.91, Acc: 0.65, Rec : 0.80, Class and Pose  : 0.65
Validation time for epoch 3: 5.58 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:47<00:00, 14.20it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:16<00:00,  2.92s/it]
Epoch-3, unseen_occ -- Mean err: 25.91, Acc: 0.66, Rec : 0.91, Class and Pose  : 0.66
Validation time for epoch 3: 2.96 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:36<00:00,  4.68it/s]5s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:46<00:00,  9.22it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:17<00:00,  4.38it/s]
Epoch-4, seen -- Mean err: 8.15, Acc: 0.91, Rec : 0.96, Class and Pose  : 0.91
Validation time for epoch 4: 2.45 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:30<00:00, 12.41it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.92it/s]
Epoch-4, unseen -- Mean err: 10.79, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 4: 6.86 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:22<00:00, 13.23it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.16it/s]
Epoch-4, seen_occ -- Mean err: 21.73, Acc: 0.64, Rec : 0.81, Class and Pose  : 0.63
Validation time for epoch 4: 5.65 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:50<00:00, 13.92it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:43<00:00,  2.96s/it]
Epoch-4, unseen_occ -- Mean err: 22.93, Acc: 0.67, Rec : 0.92, Class and Pose  : 0.67
Validation time for epoch 4: 3.03 minutes
处理模板数据:   2%|█▉                                                                                                              | 3/170 [58:29<54:15:46, 1169.74s/it]4s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
 20%|█████████████████████████                                                                                                    | 5/25 [5:30:00<22:00:02, 3960.15s/it]
Traceback (most recent call last):
  File "train_new.py", line 453, in <module>
  File "train_new.py", line 373, in main
    # 测试完成后，将模型设回训练模式
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 87, in test
    for miniBatch in tqdm(template_dataloader, desc="处理模板数据"):
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/std.py", line 1181, in __iter__
    for obj in iterable:
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 681, in __next__
    data = self._next_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1359, in _next_data
    idx, data = self._get_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1315, in _get_data
    success, data = self._try_get_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1163, in _try_get_data
    data = self._data_queue.get(timeout=timeout)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/queue.py", line 179, in get
    self.not_empty.wait(remaining)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/threading.py", line 306, in wait
    gotit = waiter.acquire(True, timeout)
KeyboardInterrupt
