特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:40<00:00,  1.80s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:20<00:00,  8.10it/s]3s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:09<00:00, 14.08it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  7.63it/s]
Epoch-0, seen -- Mean err: 4.52, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 1.57 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:08<00:00, 15.74it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.47it/s]
Epoch-0, unseen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 5.36 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:28<00:00, 15.93it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  6.85it/s]
Epoch-0, seen_occ -- Mean err: 12.58, Acc: 0.86, Rec : 0.93, Class and Pose  : 0.85
Validation time for epoch 0: 4.71 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:27<00:00, 16.07it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:27<00:00,  1.78s/it]
Epoch-0, unseen_occ -- Mean err: 8.90, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 0: 2.61 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:23<00:00,  7.35it/s]9s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:07<00:00, 14.59it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  7.80it/s]
Epoch-1, seen -- Mean err: 4.36, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 1: 1.57 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:05<00:00, 15.88it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.75it/s]
Epoch-1, unseen -- Mean err: 4.58, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 5.30 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:38<00:00, 15.35it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.00it/s]
Epoch-1, seen_occ -- Mean err: 13.43, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 1: 4.87 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:27<00:00, 16.10it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:45<00:00,  1.81s/it]
Epoch-1, unseen_occ -- Mean err: 10.87, Acc: 0.88, Rec : 0.97, Class and Pose  : 0.88
Validation time for epoch 1: 2.63 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:21<00:00,  7.97it/s]2s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:04<00:00, 15.16it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.43it/s]
Epoch-2, seen -- Mean err: 4.26, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 2: 1.50 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:02<00:00, 16.03it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.24it/s]
Epoch-2, unseen -- Mean err: 4.65, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 5.26 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:29<00:00, 15.86it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  6.72it/s]
Epoch-2, seen_occ -- Mean err: 13.96, Acc: 0.83, Rec : 0.90, Class and Pose  : 0.82
Validation time for epoch 2: 4.71 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:22<00:00, 16.65it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:38<00:00,  1.79s/it]
Epoch-2, unseen_occ -- Mean err: 12.11, Acc: 0.85, Rec : 0.97, Class and Pose  : 0.85
Validation time for epoch 2: 2.54 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:22<00:00,  7.68it/s]4s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:06<00:00, 14.68it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.79it/s]
Epoch-3, seen -- Mean err: 4.06, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 3: 1.53 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:12<00:00, 15.51it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  7.65it/s]
Epoch-3, unseen -- Mean err: 4.65, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 5.46 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [03:44<00:00, 19.07it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:04<00:00,  8.05it/s]
Epoch-3, seen_occ -- Mean err: 12.80, Acc: 0.85, Rec : 0.91, Class and Pose  : 0.84
Validation time for epoch 3: 3.95 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [01:57<00:00, 20.15it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:32<00:00,  1.79s/it]
Epoch-3, unseen_occ -- Mean err: 10.70, Acc: 0.87, Rec : 0.97, Class and Pose  : 0.86
Validation time for epoch 3: 2.08 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:21<00:00,  8.05it/s]2s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:07<00:00, 14.58it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  6.99it/s]
Epoch-4, seen -- Mean err: 4.10, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 4: 1.55 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [04:59<00:00, 16.20it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.87it/s]
Epoch-4, unseen -- Mean err: 4.63, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 5.22 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:35<00:00, 15.51it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.88it/s]
Epoch-4, seen_occ -- Mean err: 12.79, Acc: 0.85, Rec : 0.91, Class and Pose  : 0.85
Validation time for epoch 4: 4.83 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:20<00:00, 16.90it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:32<00:00,  1.79s/it]
Epoch-4, unseen_occ -- Mean err: 10.36, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 4: 2.52 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:21<00:00,  8.01it/s]6s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:06<00:00, 14.74it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.22it/s]
Epoch-5, seen -- Mean err: 4.08, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 5: 1.52 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [04:55<00:00, 16.40it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.46it/s]
Epoch-5, unseen -- Mean err: 4.77, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 5: 5.18 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:35<00:00, 15.53it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.83it/s]
Epoch-5, seen_occ -- Mean err: 13.08, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.84
Validation time for epoch 5: 4.84 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:29<00:00, 15.85it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:47<00:00,  1.81s/it]
Epoch-5, unseen_occ -- Mean err: 10.02, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 5: 2.65 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:22<00:00,  7.41it/s]7s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:06<00:00, 14.70it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.49it/s]
Epoch-6, seen -- Mean err: 4.25, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 6: 1.56 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [04:57<00:00, 16.30it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.89it/s]
Epoch-6, unseen -- Mean err: 4.80, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 6: 5.22 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:28<00:00, 15.91it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  6.57it/s]
Epoch-6, seen_occ -- Mean err: 12.85, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.84
Validation time for epoch 6: 4.72 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:23<00:00, 16.53it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:48<00:00,  1.81s/it]
Epoch-6, unseen_occ -- Mean err: 10.69, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.85
Validation time for epoch 6: 2.54 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:21<00:00,  8.04it/s]3s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:09<00:00, 14.07it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.04it/s]
Epoch-7, seen -- Mean err: 4.10, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 7: 1.59 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:14<00:00, 15.43it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  6.93it/s]
Epoch-7, unseen -- Mean err: 4.76, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 7: 5.49 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:43<00:00, 15.05it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.07it/s]
Epoch-7, seen_occ -- Mean err: 12.91, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.84
Validation time for epoch 7: 4.99 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:24<00:00, 16.50it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:30<00:00,  1.78s/it]
Epoch-7, unseen_occ -- Mean err: 10.40, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 7: 2.57 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:21<00:00,  7.87it/s]4s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:09<00:00, 14.16it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  7.99it/s]
Epoch-8, seen -- Mean err: 4.18, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 8: 1.57 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:04<00:00, 15.91it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.34it/s]
Epoch-8, unseen -- Mean err: 4.76, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 8: 5.30 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:31<00:00, 15.72it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.06it/s]
Epoch-8, seen_occ -- Mean err: 12.30, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.85
Validation time for epoch 8: 4.77 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:25<00:00, 16.30it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:19<00:00,  1.77s/it]
Epoch-8, unseen_occ -- Mean err: 10.37, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 8: 2.59 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:20<00:00,  8.18it/s]8s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:07<00:00, 14.56it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:09<00:00,  7.75it/s]
Epoch-9, seen -- Mean err: 4.08, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 9: 1.53 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [04:59<00:00, 16.20it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.17it/s]
Epoch-9, unseen -- Mean err: 4.71, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 9: 5.20 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:26<00:00, 16.02it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  6.56it/s]
Epoch-9, seen_occ -- Mean err: 12.56, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.85
Validation time for epoch 9: 4.67 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:24<00:00, 16.46it/s]
  5%|██████▋                                                                                                                           | 32/623 [01:11<22:06,  2.24s/it]
Epoch-9, unseen_occ -- Mean err: 10.20, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 9: 2.56 minutes
   📉 学习率已衰减，当前学习率: [2e-05]
 40%|██████████████████████████████████████████████████                                                                           | 10/25 [5:29:35<8:14:22, 1977.51s/it]
Traceback (most recent call last):
  File "train_new.py", line 453, in <module>
  File "train_new.py", line 338, in main
    if (epoch + 1) % 1 == 0:
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 466, in train
    loss_dict['total_loss'].backward()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/_tensor.py", line 396, in backward
    torch.autograd.backward(self, gradient, retain_graph, create_graph, inputs=inputs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/autograd/__init__.py", line 173, in backward
    Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
KeyboardInterrupt
