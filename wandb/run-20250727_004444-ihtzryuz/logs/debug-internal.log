{"time":"2025-07-27T00:44:44.597738621+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250727_004444-ihtzryuz/logs/debug-core.log"}
{"time":"2025-07-27T00:44:44.830409147+08:00","level":"INFO","msg":"created new stream","id":"ihtzryuz"}
{"time":"2025-07-27T00:44:44.830447731+08:00","level":"INFO","msg":"stream: started","id":"ihtzryuz"}
{"time":"2025-07-27T00:44:44.831920371+08:00","level":"INFO","msg":"sender: started","stream_id":"ihtzryuz"}
{"time":"2025-07-27T00:44:44.831974721+08:00","level":"INFO","msg":"handler: started","stream_id":"ihtzryuz"}
{"time":"2025-07-27T00:44:44.831991498+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ihtzryuz"}
{"time":"2025-07-27T00:44:45.66402912+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-27T00:55:21.745739075+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T00:55:29.317076533+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T01:08:06.750134407+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T01:08:14.316939137+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T01:08:24.233830188+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T01:11:50.773427887+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T01:17:20.775096419+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T01:18:58.459050637+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": unexpected EOF"}
{"time":"2025-07-27T01:20:50.774845634+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T01:21:05.778852699+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T01:25:58.228682577+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": unexpected EOF"}
{"time":"2025-07-27T01:33:20.773434482+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T01:39:35.772432786+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T01:39:43.151093285+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T01:39:53.207685958+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T04:11:05.781526028+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T08:58:50.782651141+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T10:06:35.786421354+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T11:39:35.781499568+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T15:31:20.785957252+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream\": EOF"}
{"time":"2025-07-27T16:16:01.262239612+08:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/files/jialeren/pose-estimation-723/ihtzryuz/file_stream"}
{"time":"2025-07-27T16:16:01.262317151+08:00","level":"ERROR+4","msg":"filestream: fatal error: filestream: failed to upload: 404 Not Found path=files/jialeren/pose-estimation-723/ihtzryuz/file_stream: {\"error\":\"run pose-estimation-723/ihtzryuz not found while streaming file\"}"}
