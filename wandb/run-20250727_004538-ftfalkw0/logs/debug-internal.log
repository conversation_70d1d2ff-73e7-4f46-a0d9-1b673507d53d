{"time":"2025-07-27T00:45:38.380252546+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250727_004538-ftfalkw0/logs/debug-core.log"}
{"time":"2025-07-27T00:45:40.941500256+08:00","level":"INFO","msg":"created new stream","id":"ftfalkw0"}
{"time":"2025-07-27T00:45:40.941559509+08:00","level":"INFO","msg":"stream: started","id":"ftfalkw0"}
{"time":"2025-07-27T00:45:40.941587038+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ftfalkw0"}
{"time":"2025-07-27T00:45:40.941771953+08:00","level":"INFO","msg":"handler: started","stream_id":"ftfalkw0"}
{"time":"2025-07-27T00:45:40.941804564+08:00","level":"INFO","msg":"sender: started","stream_id":"ftfalkw0"}
{"time":"2025-07-27T00:45:41.591197579+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-27T00:55:48.179115314+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T01:08:18.180354085+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T01:08:25.307010305+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T01:08:34.628014978+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T01:08:45.119614378+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-27T01:09:17.201343115+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:09:51.658549192+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-27T01:10:02.749712068+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-07-27T01:16:11.486901364+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T01:18:56.498931751+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T01:39:26.486288161+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T01:39:33.716875444+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T01:39:43.575941226+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T01:40:01.659521954+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-27T01:40:28.173944517+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T01:40:34.056442138+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:41:08.453814215+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-27T01:41:48.420244923+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:42:37.229956476+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:43:44.31254607+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:45:14.313698042+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:46:44.31749119+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:48:14.318455755+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:49:31.658812066+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.000241961,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true mailbox_slot:\"r82kf0vnb57o\" connection_id:\"127.0.0.1:34290\")"}
{"time":"2025-07-27T01:49:44.319557005+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:50:23.051671062+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000657883,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:34290\")"}
{"time":"2025-07-27T01:50:26.605754982+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.001074851,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-07-27T01:50:26.838478949+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000068372,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-07-27T01:51:14.328674048+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:52:44.332957354+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:54:14.341636988+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:55:16.160041008+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-07-27T01:56:16.724026319+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":1005.065487042,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true mailbox_slot:\"r82kf0vnb57o\" connection_id:\"127.0.0.1:34290\")"}
{"time":"2025-07-27T01:56:16.72581195+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":953.674808291,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:34290\")"}
{"time":"2025-07-27T01:56:16.726103311+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":950.121467618,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-07-27T01:56:16.729773443+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":949.891386054,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-07-27T03:29:36.827375105+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T07:28:06.828467653+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T09:09:21.827669089+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T09:46:06.827853568+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T13:14:36.827833337+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T13:39:37.241524067+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T15:57:21.832587178+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/ftfalkw0/file_stream\": EOF"}
{"time":"2025-07-27T16:41:08.266343668+08:00","level":"INFO","msg":"stream: closing","id":"ftfalkw0"}
{"time":"2025-07-27T16:41:08.26637604+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-27T16:41:08.272253057+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-27T16:41:10.161518137+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-27T16:41:11.331153801+08:00","level":"INFO","msg":"handler: closed","stream_id":"ftfalkw0"}
{"time":"2025-07-27T16:41:11.331205512+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"ftfalkw0"}
{"time":"2025-07-27T16:41:11.331274233+08:00","level":"INFO","msg":"sender: closed","stream_id":"ftfalkw0"}
{"time":"2025-07-27T16:41:11.331285748+08:00","level":"INFO","msg":"stream: closed","id":"ftfalkw0"}
