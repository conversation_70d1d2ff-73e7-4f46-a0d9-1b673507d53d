2025-07-27 00:45:38,339 INFO    MainThread:1822143 [wandb_setup.py:_flush():67] Current SDK version is 0.19.8
2025-07-27 00:45:38,340 INFO    MainThread:1822143 [wandb_setup.py:_flush():67] Configure stats pid to 1822143
2025-07-27 00:45:38,340 INFO    MainThread:1822143 [wandb_setup.py:_flush():67] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-27 00:45:38,340 INFO    MainThread:1822143 [wandb_setup.py:_flush():67] Loading settings from /home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/settings
2025-07-27 00:45:38,340 INFO    MainThread:1822143 [wandb_setup.py:_flush():67] Loading settings from environment variables
2025-07-27 00:45:38,340 INFO    MainThread:1822143 [wandb_init.py:setup_run_log_directory():647] Logging user logs to /home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250727_004538-ftfalkw0/logs/debug.log
2025-07-27 00:45:38,340 INFO    MainThread:1822143 [wandb_init.py:setup_run_log_directory():648] Logging internal logs to /home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250727_004538-ftfalkw0/logs/debug-internal.log
2025-07-27 00:45:38,340 INFO    MainThread:1822143 [wandb_init.py:init():761] calling init triggers
2025-07-27 00:45:38,340 INFO    MainThread:1822143 [wandb_init.py:init():766] wandb.init called with sweep_config: {}
config: {'learning_rate': 0.0001, 'batch_size': 16, 'epochs': 25, 'weight_decay': 0.0005, 'descriptor_size': 128, 'split': 'split1', 'feature_blocks': [9, 10, 11, 12], 'offset_prediction_enabled': False, 'offset_loss_weight': 0, '_wandb': {'code_path': 'code/train_new.py'}}
2025-07-27 00:45:38,340 INFO    MainThread:1822143 [wandb_init.py:init():784] starting backend
2025-07-27 00:45:38,340 INFO    MainThread:1822143 [wandb_init.py:init():788] sending inform_init request
2025-07-27 00:45:38,382 INFO    MainThread:1822143 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-07-27 00:45:38,382 INFO    MainThread:1822143 [wandb_init.py:init():798] backend started and connected
2025-07-27 00:45:38,385 INFO    MainThread:1822143 [wandb_init.py:init():891] updated telemetry
2025-07-27 00:45:38,510 INFO    MainThread:1822143 [wandb_init.py:init():915] communicating run to backend with 90.0 second timeout
2025-07-27 00:45:41,588 INFO    MainThread:1822143 [wandb_init.py:init():990] starting run threads in backend
2025-07-27 00:45:43,589 INFO    MainThread:1822143 [wandb_run.py:_console_start():2375] atexit reg
2025-07-27 00:45:43,590 INFO    MainThread:1822143 [wandb_run.py:_redirect():2227] redirect: wrap_raw
2025-07-27 00:45:43,590 INFO    MainThread:1822143 [wandb_run.py:_redirect():2292] Wrapping output streams.
2025-07-27 00:45:43,590 INFO    MainThread:1822143 [wandb_run.py:_redirect():2315] Redirects installed.
2025-07-27 00:45:43,592 INFO    MainThread:1822143 [wandb_init.py:init():1032] run started, returning control to user process
2025-07-27 16:41:08,265 INFO    MsgRouterThr:1822143 [mailbox.py:close():129] Closing mailbox, abandoning 1 handles.
