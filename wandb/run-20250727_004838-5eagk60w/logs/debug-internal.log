{"time":"2025-07-27T00:48:38.428113541+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250727_004838-5eagk60w/logs/debug-core.log"}
{"time":"2025-07-27T00:48:38.659177399+08:00","level":"INFO","msg":"created new stream","id":"5eagk60w"}
{"time":"2025-07-27T00:48:38.659231814+08:00","level":"INFO","msg":"stream: started","id":"5eagk60w"}
{"time":"2025-07-27T00:48:38.659335915+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"5eagk60w"}
{"time":"2025-07-27T00:48:38.659375301+08:00","level":"INFO","msg":"handler: started","stream_id":"5eagk60w"}
{"time":"2025-07-27T00:48:38.659453754+08:00","level":"INFO","msg":"sender: started","stream_id":"5eagk60w"}
{"time":"2025-07-27T00:48:39.379865309+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-27T01:08:15.898681107+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T01:08:23.342755931+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T01:08:33.287343028+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T01:08:42.853400113+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:09:15.101904632+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:09:49.367004516+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:10:02.750495216+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-07-27T01:10:56.71770353+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T01:19:08.604854897+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": unexpected EOF"}
{"time":"2025-07-27T01:19:18.129049946+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T01:20:50.143472638+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T01:21:05.142194336+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T01:23:35.148993223+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T01:39:35.143218619+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T01:39:42.575262635+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T01:39:52.106027711+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T02:26:38.772649615+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T04:47:23.773295556+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T07:06:23.777504302+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T08:51:08.774265069+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/5eagk60w/file_stream\": EOF"}
{"time":"2025-07-27T16:41:05.589128098+08:00","level":"INFO","msg":"stream: closing","id":"5eagk60w"}
{"time":"2025-07-27T16:41:05.58915899+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-27T16:41:05.590160093+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-27T16:41:07.139110636+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-27T16:41:08.471947094+08:00","level":"INFO","msg":"handler: closed","stream_id":"5eagk60w"}
{"time":"2025-07-27T16:41:08.472023821+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"5eagk60w"}
{"time":"2025-07-27T16:41:08.472108535+08:00","level":"INFO","msg":"sender: closed","stream_id":"5eagk60w"}
{"time":"2025-07-27T16:41:08.472168168+08:00","level":"INFO","msg":"stream: closed","id":"5eagk60w"}
