{"time":"2025-07-27T00:51:02.03654247+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250727_005101-yuige01y/logs/debug-core.log"}
{"time":"2025-07-27T00:51:02.387408799+08:00","level":"INFO","msg":"created new stream","id":"yuige01y"}
{"time":"2025-07-27T00:51:02.387494923+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"yuige01y"}
{"time":"2025-07-27T00:51:02.387590946+08:00","level":"INFO","msg":"handler: started","stream_id":"yuige01y"}
{"time":"2025-07-27T00:51:02.387605112+08:00","level":"INFO","msg":"stream: started","id":"yuige01y"}
{"time":"2025-07-27T00:51:02.387642965+08:00","level":"INFO","msg":"sender: started","stream_id":"yuige01y"}
{"time":"2025-07-27T00:51:03.083958585+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-27T01:08:09.440201832+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T01:08:16.552549334+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T01:08:25.89057267+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T01:08:36.666550357+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-27T01:09:08.737838922+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-07-27T01:09:43.035283807+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-07-27T01:09:56.60228859+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-07-27T01:12:22.599009125+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T01:20:52.59413186+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T01:39:37.594874086+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T01:39:45.053864842+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T01:39:54.524218008+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T02:03:54.512951552+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T04:24:39.513066633+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T07:27:39.512603029+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T08:38:24.513116839+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T10:15:24.513486673+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T15:34:39.512968799+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T15:35:39.512478151+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/yuige01y/file_stream\": EOF"}
{"time":"2025-07-27T15:37:02.994649532+08:00","level":"INFO","msg":"stream: closing","id":"yuige01y"}
{"time":"2025-07-27T15:37:02.994688157+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-27T15:37:02.995529277+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-27T15:37:05.1109001+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-27T15:37:05.693882831+08:00","level":"INFO","msg":"handler: closed","stream_id":"yuige01y"}
{"time":"2025-07-27T15:37:05.693942187+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"yuige01y"}
{"time":"2025-07-27T15:37:05.694016924+08:00","level":"INFO","msg":"sender: closed","stream_id":"yuige01y"}
{"time":"2025-07-27T15:37:05.694029032+08:00","level":"INFO","msg":"stream: closed","id":"yuige01y"}
