特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.14M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.14M
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [31:43<00:00,  3.06s/it]

🔄 Epoch 0: 切换到Hash训练阶段（实验1：简化版本）
   🎯 目标：复现wrap版本的训练方式
   📝 配置：只使用Hash特征损失 + Hash正则化损失
   📊 简化训练参数统计:
      - 主要模型: 1.43M
      - Hash编码器: 0.14M
      - 总计: 1.56M
   📈 统一学习率: 0.0001
   ✅ 优化器已切换到简化Hash训练模式（等价wrap版本）
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:33<00:00,  5.09it/s]3s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:24<00:00, 11.62it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:13<00:00,  5.78it/s]
Epoch-0, seen -- Mean err: 4.66, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 2.03 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:05<00:00, 13.26it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.06it/s]
Epoch-0, unseen -- Mean err: 4.73, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 6.39 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:32<00:00, 12.84it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:09<00:00,  4.21it/s]
Epoch-0, seen_occ -- Mean err: 13.19, Acc: 0.84, Rec : 0.92, Class and Pose  : 0.83
Validation time for epoch 0: 5.79 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:46<00:00, 14.26it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [31:42<00:00,  3.05s/it]
Epoch-0, unseen_occ -- Mean err: 10.64, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 0: 2.99 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:27<00:00,  6.27it/s]5s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:24<00:00, 11.57it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.10it/s]
Epoch-1, seen -- Mean err: 4.56, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 1: 1.93 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:03<00:00, 13.33it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.32it/s]
Epoch-1, unseen -- Mean err: 5.01, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 6.34 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:25<00:00, 13.14it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.32it/s]
Epoch-1, seen_occ -- Mean err: 15.27, Acc: 0.80, Rec : 0.87, Class and Pose  : 0.79
Validation time for epoch 1: 5.69 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:49<00:00, 14.05it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [31:29<00:00,  3.03s/it]
Epoch-1, unseen_occ -- Mean err: 12.28, Acc: 0.85, Rec : 0.96, Class and Pose  : 0.84
Validation time for epoch 1: 3.00 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:26<00:00,  6.49it/s]5s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:22<00:00, 11.83it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.61it/s]
Epoch-2, seen -- Mean err: 4.75, Acc: 0.99, Rec : 0.99, Class and Pose  : 0.99
Validation time for epoch 2: 1.89 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:16<00:00, 12.87it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.20it/s]
Epoch-2, unseen -- Mean err: 5.40, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 2: 6.52 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:27<00:00, 13.06it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.71it/s]
Epoch-2, seen_occ -- Mean err: 17.36, Acc: 0.75, Rec : 0.85, Class and Pose  : 0.75
Validation time for epoch 2: 5.74 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:04<00:00, 12.86it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [31:15<00:00,  3.01s/it]
Epoch-2, unseen_occ -- Mean err: 15.00, Acc: 0.81, Rec : 0.95, Class and Pose  : 0.81
Validation time for epoch 2: 3.26 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.59it/s]1s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:30<00:00, 10.90it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.99it/s]
Epoch-3, seen -- Mean err: 4.73, Acc: 0.99, Rec : 0.99, Class and Pose  : 0.99
Validation time for epoch 3: 2.00 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:12<00:00, 13.01it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.29it/s]
Epoch-3, unseen -- Mean err: 5.41, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 3: 6.49 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:25<00:00, 13.14it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.32it/s]
Epoch-3, seen_occ -- Mean err: 16.70, Acc: 0.77, Rec : 0.85, Class and Pose  : 0.76
Validation time for epoch 3: 5.69 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:12<00:00, 12.35it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [31:01<00:00,  2.99s/it]
Epoch-3, unseen_occ -- Mean err: 15.08, Acc: 0.81, Rec : 0.93, Class and Pose  : 0.81
Validation time for epoch 3: 3.39 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:26<00:00,  6.42it/s]5s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:23<00:00, 11.68it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.38it/s]
Epoch-4, seen -- Mean err: 5.35, Acc: 0.98, Rec : 0.99, Class and Pose  : 0.98
Validation time for epoch 4: 1.92 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [06:16<00:00, 12.86it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  6.08it/s]
Epoch-4, unseen -- Mean err: 7.10, Acc: 0.95, Rec : 0.99, Class and Pose  : 0.95
Validation time for epoch 4: 6.55 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:27<00:00, 13.06it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:07<00:00,  5.31it/s]
Epoch-4, seen_occ -- Mean err: 17.42, Acc: 0.76, Rec : 0.85, Class and Pose  : 0.75
Validation time for epoch 4: 5.73 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:23<00:00, 11.67it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:54<00:00,  2.98s/it]
Epoch-4, unseen_occ -- Mean err: 17.47, Acc: 0.75, Rec : 0.91, Class and Pose  : 0.74
Validation time for epoch 4: 3.58 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:24<00:00,  6.94it/s]3s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:07<00:00, 14.46it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.78it/s]
Epoch-5, seen -- Mean err: 5.79, Acc: 0.97, Rec : 0.98, Class and Pose  : 0.96
Validation time for epoch 5: 1.58 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [04:39<00:00, 17.35it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.85it/s]
Epoch-5, unseen -- Mean err: 7.89, Acc: 0.94, Rec : 0.98, Class and Pose  : 0.94
Validation time for epoch 5: 4.91 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:10<00:00, 13.75it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.12it/s]
Epoch-5, seen_occ -- Mean err: 18.49, Acc: 0.73, Rec : 0.83, Class and Pose  : 0.72
Validation time for epoch 5: 5.46 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:03<00:00, 12.96it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [31:15<00:00,  3.01s/it]
Epoch-5, unseen_occ -- Mean err: 18.61, Acc: 0.74, Rec : 0.90, Class and Pose  : 0.74
Validation time for epoch 5: 3.22 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.75it/s]4s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:00<00:00, 16.21it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.35it/s]
Epoch-6, seen -- Mean err: 6.12, Acc: 0.96, Rec : 0.97, Class and Pose  : 0.95
Validation time for epoch 6: 1.48 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:06<00:00, 15.80it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.01it/s]
Epoch-6, unseen -- Mean err: 8.43, Acc: 0.93, Rec : 0.97, Class and Pose  : 0.93
Validation time for epoch 6: 5.36 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [03:50<00:00, 18.54it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.25it/s]
Epoch-6, seen_occ -- Mean err: 19.50, Acc: 0.71, Rec : 0.81, Class and Pose  : 0.70
Validation time for epoch 6: 4.07 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:20<00:00, 16.91it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [30:51<00:00,  2.97s/it]
Epoch-6, unseen_occ -- Mean err: 20.70, Acc: 0.70, Rec : 0.88, Class and Pose  : 0.69
Validation time for epoch 6: 2.49 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:25<00:00,  6.61it/s]8s/it]

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [00:55<00:00, 17.82it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.52it/s]
Epoch-7, seen -- Mean err: 6.28, Acc: 0.96, Rec : 0.97, Class and Pose  : 0.96
Validation time for epoch 7: 1.38 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [04:22<00:00, 18.47it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.49it/s]
Epoch-7, unseen -- Mean err: 8.99, Acc: 0.91, Rec : 0.97, Class and Pose  : 0.91
Validation time for epoch 7: 4.61 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [03:41<00:00, 19.27it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.21it/s]
Epoch-7, seen_occ -- Mean err: 19.17, Acc: 0.71, Rec : 0.82, Class and Pose  : 0.70
Validation time for epoch 7: 3.92 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:46<00:00, 14.32it/s]
  0%|▏                                                                                                                                | 1/623 [00:12<2:09:23, 12.48s/it]
Epoch-7, unseen_occ -- Mean err: 22.14, Acc: 0.67, Rec : 0.89, Class and Pose  : 0.66
Validation time for epoch 7: 2.90 minutes
✅ Hash编码器已设置为训练模式
 32%|████████████████████████████████████████                                                                                     | 8/25 [6:19:54<13:27:18, 2849.33s/it]
Traceback (most recent call last):
  File "train_new.py", line 437, in <module>
    main()
  File "train_new.py", line 322, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 65, in train
    for i, miniBatch in enumerate(tqdm(train_loader)):
  File "/home/<USER>/.local/lib/python3.8/site-packages/tqdm/std.py", line 1181, in __iter__
    for obj in iterable:
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 681, in __next__
    data = self._next_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1359, in _next_data
    idx, data = self._get_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1315, in _get_data
    success, data = self._try_get_data()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1163, in _try_get_data
    data = self._data_queue.get(timeout=timeout)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/queue.py", line 179, in get
    self.not_empty.wait(remaining)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/threading.py", line 306, in wait
    gotit = waiter.acquire(True, timeout)
KeyboardInterrupt
