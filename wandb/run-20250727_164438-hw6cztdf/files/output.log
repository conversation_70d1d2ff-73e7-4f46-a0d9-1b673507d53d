特征提取器可训练参数数量: 1.43M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 1.43M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:58<00:00,  1.83s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:21<00:00,  7.73it/s]7s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:09<00:00, 14.12it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:12<00:00,  5.87it/s]
Epoch-0, seen -- Mean err: 4.52, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 1.60 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:02<00:00, 16.04it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.34it/s]
Epoch-0, unseen -- Mean err: 4.56, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 5.31 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:32<00:00, 15.65it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.63it/s]
Epoch-0, seen_occ -- Mean err: 12.58, Acc: 0.86, Rec : 0.93, Class and Pose  : 0.85
Validation time for epoch 0: 4.80 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:19<00:00, 17.04it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:41<00:00,  1.80s/it]
Epoch-0, unseen_occ -- Mean err: 8.90, Acc: 0.89, Rec : 0.98, Class and Pose  : 0.89
Validation time for epoch 0: 2.49 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:22<00:00,  7.56it/s]9s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:10<00:00, 13.89it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.44it/s]
Epoch-1, seen -- Mean err: 4.36, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 1: 1.60 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:09<00:00, 15.67it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.49it/s]
Epoch-1, unseen -- Mean err: 4.58, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 5.39 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:31<00:00, 15.71it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.02it/s]
Epoch-1, seen_occ -- Mean err: 13.43, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.83
Validation time for epoch 1: 4.75 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:20<00:00, 16.90it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:29<00:00,  1.78s/it]
Epoch-1, unseen_occ -- Mean err: 10.87, Acc: 0.88, Rec : 0.97, Class and Pose  : 0.88
Validation time for epoch 1: 2.52 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:21<00:00,  8.05it/s]6s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:04<00:00, 15.12it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.24it/s]
Epoch-2, seen -- Mean err: 4.26, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 2: 1.49 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:08<00:00, 15.70it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.40it/s]
Epoch-2, unseen -- Mean err: 4.65, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 5.37 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:28<00:00, 15.91it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.04it/s]
Epoch-2, seen_occ -- Mean err: 13.96, Acc: 0.83, Rec : 0.90, Class and Pose  : 0.82
Validation time for epoch 2: 4.70 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:28<00:00, 15.95it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:13<00:00,  1.75s/it]
Epoch-2, unseen_occ -- Mean err: 12.11, Acc: 0.85, Rec : 0.97, Class and Pose  : 0.85
Validation time for epoch 2: 2.65 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:20<00:00,  8.30it/s]5s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:05<00:00, 14.99it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.13it/s]
Epoch-3, seen -- Mean err: 4.06, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 3: 1.50 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:06<00:00, 15.79it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.31it/s]
Epoch-3, unseen -- Mean err: 4.65, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 3: 5.35 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:28<00:00, 15.91it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.24it/s]
Epoch-3, seen_occ -- Mean err: 12.80, Acc: 0.85, Rec : 0.91, Class and Pose  : 0.84
Validation time for epoch 3: 4.70 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:17<00:00, 17.28it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:37<00:00,  1.79s/it]
Epoch-3, unseen_occ -- Mean err: 10.70, Acc: 0.87, Rec : 0.97, Class and Pose  : 0.86
Validation time for epoch 3: 2.46 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:22<00:00,  7.68it/s]9s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:10<00:00, 13.86it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.12it/s]
Epoch-4, seen -- Mean err: 4.10, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 4: 1.61 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:01<00:00, 16.06it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:11<00:00,  6.88it/s]
Epoch-4, unseen -- Mean err: 4.63, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 4: 5.28 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [04:34<00:00, 15.58it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  6.28it/s]
Epoch-4, seen_occ -- Mean err: 12.79, Acc: 0.85, Rec : 0.91, Class and Pose  : 0.85
Validation time for epoch 4: 4.82 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:32<00:00, 15.60it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [21:20<00:00,  2.06s/it]
Epoch-4, unseen_occ -- Mean err: 10.36, Acc: 0.88, Rec : 0.98, Class and Pose  : 0.88
Validation time for epoch 4: 2.70 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:20<00:00,  8.16it/s]0s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:06<00:00, 14.70it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.28it/s]
Epoch-5, seen -- Mean err: 4.08, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 5: 1.51 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:33<00:00, 14.56it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.17it/s]
Epoch-5, unseen -- Mean err: 4.77, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 5: 5.78 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [14:45<00:00,  4.82it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:05<00:00,  6.55it/s]
Epoch-5, seen_occ -- Mean err: 13.08, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.84
Validation time for epoch 5: 14.99 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:24<00:00, 16.42it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [18:25<00:00,  1.77s/it]
Epoch-5, unseen_occ -- Mean err: 10.02, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.87
Validation time for epoch 5: 2.55 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:21<00:00,  7.85it/s]0s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:06<00:00, 14.82it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.57it/s]
Epoch-6, seen -- Mean err: 4.25, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 6: 1.53 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [05:52<00:00, 13.75it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.39it/s]
Epoch-6, unseen -- Mean err: 4.80, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 6: 6.10 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:01<00:00, 14.19it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.98it/s]
Epoch-6, seen_occ -- Mean err: 12.85, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.84
Validation time for epoch 6: 5.23 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [02:32<00:00, 15.64it/s]
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [20:48<00:00,  2.00s/it]
Epoch-6, unseen_occ -- Mean err: 10.69, Acc: 0.86, Rec : 0.98, Class and Pose  : 0.85
Validation time for epoch 6: 2.71 minutes
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 170/170 [00:22<00:00,  7.58it/s]9s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 981/981 [01:36<00:00, 10.13it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.16it/s]
Epoch-7, seen -- Mean err: 4.10, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 7: 2.09 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4848/4848 [24:11<00:00,  3.34it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 76/76 [00:10<00:00,  7.17it/s]
Epoch-7, unseen -- Mean err: 4.76, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 7: 24.40 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4272/4272 [05:43<00:00, 12.43it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:06<00:00,  5.85it/s]
Epoch-7, seen_occ -- Mean err: 12.91, Acc: 0.84, Rec : 0.91, Class and Pose  : 0.84
Validation time for epoch 7: 5.98 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2377/2377 [03:30<00:00, 11.30it/s]
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 623/623 [1:12:25<00:00,  6.98s/it]
Epoch-7, unseen_occ -- Mean err: 10.40, Acc: 0.87, Rec : 0.98, Class and Pose  : 0.86
Validation time for epoch 7: 3.67 minutes
处理模板数据:  22%|█████████████████████████▉                                                                                          | 38/170 [02:50<09:50,  4.48s/it]2s/it]

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
 32%|████████████████████████████████████████                                                                                     | 8/25 [6:18:55<13:25:13, 2841.94s/it]
Traceback (most recent call last):
  File "train_new.py", line 437, in <module>
    main()
  File "train_new.py", line 357, in main
    testing_score = testing_utils_dino.test(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/testing_utils_dino.py", line 93, in test
    original_features = model(template)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 258, in forward
    cls_tokens_raw, patch_tokens_raw = self.dino_extractor(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/new_dino_network.py", line 57, in forward
    _ = self.model(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 325, in forward
    ret = self.forward_features(*args, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/models/vision_transformer.py", line 261, in forward_features
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 254, in forward
    return super().forward(x_or_x_list)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 112, in forward
    x = x + attn_residual_func(x)
  File "/home/<USER>/.cache/torch/hub/facebookresearch_dinov2_main/dinov2/layers/block.py", line 91, in attn_residual_func
    return self.ls1(self.attn(self.norm1(x)))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/normalization.py", line 189, in forward
    return F.layer_norm(
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/functional.py", line 2503, in layer_norm
    return torch.layer_norm(input, normalized_shape, weight, bias, eps, torch.backends.cudnn.enabled)
KeyboardInterrupt
