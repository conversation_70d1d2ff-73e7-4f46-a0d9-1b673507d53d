{"time":"2025-07-28T10:40:56.948355516+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"/home/<USER>/projects/RJL2025/my-diff-feats-pose/wandb/run-20250728_104056-uslh3qv1/logs/debug-core.log"}
{"time":"2025-07-28T10:40:57.120888996+08:00","level":"INFO","msg":"created new stream","id":"uslh3qv1"}
{"time":"2025-07-28T10:40:57.120932637+08:00","level":"INFO","msg":"stream: started","id":"uslh3qv1"}
{"time":"2025-07-28T10:40:57.121253799+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"uslh3qv1"}
{"time":"2025-07-28T10:40:57.121273375+08:00","level":"INFO","msg":"handler: started","stream_id":"uslh3qv1"}
{"time":"2025-07-28T10:40:57.121378033+08:00","level":"INFO","msg":"sender: started","stream_id":"uslh3qv1"}
{"time":"2025-07-28T10:40:57.806865079+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T12:19:48.843503877+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/uslh3qv1/file_stream\": EOF"}
{"time":"2025-07-28T12:19:56.00958174+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/uslh3qv1/file_stream\": EOF"}
{"time":"2025-07-28T12:20:05.567539951+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/uslh3qv1/file_stream\": EOF"}
{"time":"2025-07-28T12:20:19.001066582+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/uslh3qv1/file_stream\": EOF"}
{"time":"2025-07-28T12:20:41.384208035+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/uslh3qv1/file_stream\": EOF"}
{"time":"2025-07-28T13:42:21.544963876+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/jialeren/pose-estimation-723/uslh3qv1/file_stream\": EOF"}
{"time":"2025-07-28T14:28:44.336646779+08:00","level":"INFO","msg":"stream: closing","id":"uslh3qv1"}
{"time":"2025-07-28T14:28:44.336686531+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T14:28:44.339588333+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T14:28:45.371119239+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-28T14:28:47.232192128+08:00","level":"INFO","msg":"handler: closed","stream_id":"uslh3qv1"}
{"time":"2025-07-28T14:28:47.232249651+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"uslh3qv1"}
{"time":"2025-07-28T14:28:47.232331048+08:00","level":"INFO","msg":"sender: closed","stream_id":"uslh3qv1"}
{"time":"2025-07-28T14:28:47.232342563+08:00","level":"INFO","msg":"stream: closed","id":"uslh3qv1"}
