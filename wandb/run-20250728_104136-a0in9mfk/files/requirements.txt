fonttools==4.57.0
scipy==1.10.1
numpy==1.24.4
contourpy==1.1.1
joblib==1.4.2
zipp==3.20.2
kiwisolver==1.4.7
importlib_resources==6.4.5
cycler==0.12.1
scikit-learn==1.3.2
matplotlib==3.7.5
threadpoolctl==3.5.0
tqdm==4.67.1
charset-normalizer==3.3.2
python-dateutil==2.9.0.post0
python-dateutil==2.9.0
eval_type_backport==0.2.2
typing_extensions==4.12.2
gitdb==4.0.12
google-auth-oauthlib==1.0.0
jupyter_core==5.7.2
ipython==8.12.2
pillow==10.2.0
bleach==1.5.0
networkx==3.1
importlib_metadata==7.0.2
scipy==1.10.1
asttokens==3.0.0
mkl-random==1.2.2
requests==2.31.0
psutil==5.9.8
psutil==5.9.1
smmap==5.0.2
oauthlib==3.2.2
tokenizers==0.13.3
opencv-python==********
packaging==24.2
packaging==24.0
h5py==3.10.0
mkl-service==2.4.0
diffusers==0.14.0
mdurl==0.1.2
idna==3.6
pyzmq==25.1.2
GitPython==3.1.44
entrypoints==0.4
easydict==1.13
contourpy==1.1.1
rich==13.9.4
Pygments==2.18.0
gpustat==1.1.1
PyOpenGL==3.1.0
joblib==1.4.2
pyrender==0.1.45
google-auth==2.28.2
setuptools==68.2.2
progressbar==2.5
MarkupSafe==2.1.5
freetype-py==2.4.0
markdown-it-py==3.0.0
tqdm==4.66.2
ipykernel==6.29.5
absl-py==2.1.0
wcwidth==0.2.13
torchaudio==0.12.1
html5lib==0.9999999
pure_eval==0.2.3
stack-data==0.6.2
pip==23.3.1
pyglet==2.0.14
Werkzeug==3.0.1
annotated-types==0.7.0
Brotli==1.0.9
transformers==4.27.4
wandb==0.19.8
fsspec==2024.2.0
jupyter-client==7.3.4
jedi==0.19.1
cachetools==5.3.3
wheel==0.41.2
pydantic==2.10.6
pyparsing==3.1.2
sentry-sdk==2.22.0
pyasn1-modules==0.3.0
torch==1.12.1
pickleshare==0.7.5
pyasn1==0.5.1
filelock==3.13.1
kiwisolver==1.4.5
parso==0.8.4
numpy==1.20.3
zipp==3.18.0
Markdown==3.3.4
prompt_toolkit==3.0.48
backcall==0.2.0
ruamel.yaml==0.17.16
trimesh==4.2.0
pandas==2.0.3
nest_asyncio==1.6.0
nvidia-ml-py==12.560.30
mkl-fft==1.3.1
traitlets==5.14.3
urllib3==2.2.1
olefile==0.47
accelerate==0.28.0
cycler==0.12.1
protobuf==5.26.0
regex==2023.12.25
pytz==2024.1
rsa==4.9
matplotlib-inline==0.1.7
tornado==6.1
fonttools==4.49.0
torchvision==0.13.1
scikit-learn==1.3.2
click==8.1.8
requests-oauthlib==1.4.0
setproctitle==1.3.5
executing==2.1.0
certifi==2024.8.30
comm==0.2.2
imageio==2.34.0
matplotlib==3.7.5
PySocks==1.7.1
shutup==0.2.0
pexpect==4.9.0
blessed==1.20.0
PyYAML==5.4.1
threadpoolctl==3.5.0
docker-pycreds==0.4.0
seaborn==0.13.2
debugpy==1.6.7
mathutils==2.81.2
tabulate==0.9.0
importlib_resources==6.3.0
ruamel.yaml.clib==0.2.6
huggingface-hub==0.21.4
platformdirs==4.3.6
decorator==5.1.1
six==1.16.0
safetensors==0.4.2
tzdata==2024.1
segment-anything==1.0
grpcio==1.62.1
pydantic_core==2.27.2
blenderproc==2.7.0
ptyprocess==0.7.0
