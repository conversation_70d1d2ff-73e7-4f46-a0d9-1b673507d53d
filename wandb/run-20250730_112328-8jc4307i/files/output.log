特征提取器可训练参数数量: 3.21M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 3.21M
100%|███████████████████████████████████████████| 621/621 [1:00:47<00:00,  5.87s/it]
处理模板数据: 100%|███████████████████████████████| 170/170 [02:02<00:00,  1.38it/s]5s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [02:51<00:00,  5.73it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [01:17<00:00,  1.02s/it]
Epoch-0, seen -- Mean err: 4.22, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 4.99 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4874/4874 [18:10<00:00,  4.47it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [01:24<00:00,  1.12s/it]
Epoch-0, unseen -- Mean err: 4.10, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 19.50 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4065/4065 [12:20<00:00,  5.49it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:55<00:00,  1.37it/s]
Epoch-0, seen_occ -- Mean err: 11.34, Acc: 0.85, Rec : 0.97, Class and Pose  : 0.85
Validation time for epoch 0: 14.84 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [10:25<00:00,  6.83it/s]
100%|███████████████████████████████████████████| 621/621 [1:05:14<00:00,  6.30s/it]
Epoch-0, unseen_occ -- Mean err: 13.34, Acc: 0.80, Rec : 0.93, Class and Pose  : 0.80
Validation time for epoch 0: 11.39 minutes
处理模板数据: 100%|███████████████████████████████| 170/170 [01:19<00:00,  2.14it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████| 981/981 [02:43<00:00,  5.99it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:35<00:00,  2.14it/s]
Epoch-1, seen -- Mean err: 4.30, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 4.09 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4874/4874 [09:14<00:00,  8.79it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:42<00:00,  1.79it/s]
Epoch-1, unseen -- Mean err: 4.40, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 9.91 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4065/4065 [07:31<00:00,  9.00it/s]
处理模板数据: 100%|█████████████████████████████████| 76/76 [00:35<00:00,  2.13it/s]
Epoch-1, seen_occ -- Mean err: 12.30, Acc: 0.84, Rec : 0.96, Class and Pose  : 0.84
Validation time for epoch 1: 8.37 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████| 4272/4272 [07:39<00:00,  9.30it/s]
 98%|█████████████████████████████████████████▉ | 606/621 [1:06:45<01:39,  6.61s/it]
Epoch-1, unseen_occ -- Mean err: 13.21, Acc: 0.80, Rec : 0.94, Class and Pose  : 0.79
Validation time for epoch 1: 8.35 minutes
  8%|███▎                                     | 2/25 [4:34:29<52:36:43, 8234.95s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
Traceback (most recent call last):
  File "train_new.py", line 443, in <module>
    main()
  File "train_new.py", line 328, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 173, in train
    neg_features = model(neg)  # 返回字典
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 883, in forward
    sam_cls_tokens, sam_patch_tokens = self.extract_sam_features(x, self.dino_layer_indices)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/simple_dino_feature_network.py", line 834, in extract_sam_features
    _ = self.sam_encoder.image_encoder(x_resized)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 254, in forward
    x = blk(x)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 138, in forward
    x = x + self.attn(self.norm1(x))
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/models/../../EfficientSAM/efficient_sam/efficient_sam_encoder.py", line 83, in forward
    attn = attn.softmax(dim=-1)
KeyboardInterrupt
