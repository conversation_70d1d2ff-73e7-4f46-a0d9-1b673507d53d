特征提取器可训练参数数量: 2.36M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 2.36M
100%|██████████████████████████████████████████| 621/621 [1:16:54<00:00,  7.43s/it]
处理模板数据: 100%|██████████████████████████████| 170/170 [01:12<00:00,  2.33it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [00:57<00:00, 17.20it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:35<00:00,  2.14it/s]
Epoch-0, seen -- Mean err: 4.44, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 2.20 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [04:24<00:00, 18.40it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.32it/s]
Epoch-0, unseen -- Mean err: 4.53, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 5.04 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [03:24<00:00, 19.86it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.34it/s]
Epoch-0, seen_occ -- Mean err: 11.53, Acc: 0.85, Rec : 0.96, Class and Pose  : 0.85
Validation time for epoch 0: 3.98 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [03:53<00:00, 18.27it/s]
100%|██████████████████████████████████████████| 621/621 [1:04:55<00:00,  6.27s/it]
Epoch-0, unseen_occ -- Mean err: 16.10, Acc: 0.75, Rec : 0.86, Class and Pose  : 0.75
Validation time for epoch 0: 4.46 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.33it/s]6s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:03<00:00, 15.49it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-1, seen -- Mean err: 4.52, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 2.33 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [04:19<00:00, 18.76it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-1, unseen -- Mean err: 4.67, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 4.91 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [04:47<00:00, 14.14it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.33it/s]
Epoch-1, seen_occ -- Mean err: 11.10, Acc: 0.86, Rec : 0.92, Class and Pose  : 0.85
Validation time for epoch 1: 5.38 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [04:32<00:00, 15.70it/s]
100%|██████████████████████████████████████████| 621/621 [1:02:46<00:00,  6.07s/it]
Epoch-1, unseen_occ -- Mean err: 15.29, Acc: 0.77, Rec : 0.90, Class and Pose  : 0.77
Validation time for epoch 1: 5.12 minutes
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.30it/s]1s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:02<00:00, 15.70it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.29it/s]
Epoch-2, seen -- Mean err: 4.37, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 2.31 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [04:28<00:00, 18.17it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-2, unseen -- Mean err: 4.58, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 5.05 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [03:32<00:00, 19.11it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.26it/s]
Epoch-2, seen_occ -- Mean err: 11.72, Acc: 0.85, Rec : 0.95, Class and Pose  : 0.85
Validation time for epoch 2: 4.12 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [03:31<00:00, 20.21it/s]
 31%|█████████████▌                              | 191/621 [19:29<43:52,  6.12s/it]
Epoch-2, unseen_occ -- Mean err: 15.68, Acc: 0.76, Rec : 0.90, Class and Pose  : 0.76
Validation time for epoch 2: 4.13 minutes
 12%|████▊                                   | 3/25 [4:33:26<33:25:11, 5468.72s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
Traceback (most recent call last):
  File "train_new.py", line 443, in <module>
    main()
  File "train_new.py", line 328, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 499, in train
    loss_dict['total_loss'].backward()
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/_tensor.py", line 396, in backward
    torch.autograd.backward(self, gradient, retain_graph, create_graph, inputs=inputs)
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/autograd/__init__.py", line 173, in backward
    Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
KeyboardInterrupt
