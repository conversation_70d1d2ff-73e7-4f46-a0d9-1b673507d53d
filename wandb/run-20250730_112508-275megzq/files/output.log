特征提取器可训练参数数量: 3.21M
❌ 偏移量预测功能未启用
Hash编码器可训练参数数量: 0.13M
✅ Hash编码功能已启用
   📅 Hash训练将从epoch 0开始
   📊 Hash阶段优化器参数数量: 0.13M
初始阶段优化器参数数量: 3.21M
100%|██████████████████████████████████████████| 621/621 [1:09:01<00:00,  6.67s/it]

🔄 Epoch 0: 切换到Hash训练阶段（实验1：简化版本）
   🎯 目标：复现wrap版本的训练方式
   📝 配置：只使用Hash特征损失 + Hash正则化损失
   📊 简化训练参数统计:
      - 主要模型: 3.21M
      - Hash编码器: 0.13M
      - 总计: 3.35M
   📈 统一学习率: 0.0001
   ✅ 优化器已切换到简化Hash训练模式（等价wrap版本）
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:15<00:00,  2.24it/s]7s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:02<00:00, 15.66it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:34<00:00,  2.20it/s]
Epoch-0, seen -- Mean err: 4.37, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 2.36 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [04:57<00:00, 16.40it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:34<00:00,  2.23it/s]
Epoch-0, unseen -- Mean err: 4.20, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 5.57 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [03:52<00:00, 17.49it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:33<00:00,  2.25it/s]
Epoch-0, seen_occ -- Mean err: 11.78, Acc: 0.85, Rec : 0.96, Class and Pose  : 0.85
Validation time for epoch 0: 4.48 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [03:32<00:00, 20.11it/s]
100%|██████████████████████████████████████████| 621/621 [1:01:51<00:00,  5.98s/it]
Epoch-0, unseen_occ -- Mean err: 14.11, Acc: 0.79, Rec : 0.91, Class and Pose  : 0.78
Validation time for epoch 0: 4.14 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:14<00:00,  2.30it/s]2s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:15<00:00, 13.07it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:35<00:00,  2.15it/s]
Epoch-1, seen -- Mean err: 4.29, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 1: 2.53 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [05:04<00:00, 16.03it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-1, unseen -- Mean err: 4.26, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 1: 5.70 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [03:34<00:00, 18.96it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-1, seen_occ -- Mean err: 11.72, Acc: 0.85, Rec : 0.96, Class and Pose  : 0.85
Validation time for epoch 1: 4.16 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [03:27<00:00, 20.63it/s]
100%|██████████████████████████████████████████| 621/621 [1:01:11<00:00,  5.91s/it]
Epoch-1, unseen_occ -- Mean err: 14.44, Acc: 0.79, Rec : 0.90, Class and Pose  : 0.79
Validation time for epoch 1: 4.03 minutes
✅ Hash编码器已设置为训练模式
处理模板数据: 100%|██████████████████████████████| 170/170 [01:13<00:00,  2.32it/s]8s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True

Testing seen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|██████████████████████████████| 981/981 [01:09<00:00, 14.20it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-2, seen -- Mean err: 4.34, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 2.41 minutes

Testing unseen...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4874/4874 [04:18<00:00, 18.85it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.31it/s]
Epoch-2, unseen -- Mean err: 4.52, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 2: 4.89 minutes

Testing seen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4065/4065 [03:34<00:00, 18.92it/s]
处理模板数据: 100%|████████████████████████████████| 76/76 [00:32<00:00,  2.32it/s]
Epoch-2, seen_occ -- Mean err: 12.63, Acc: 0.84, Rec : 0.95, Class and Pose  : 0.84
Validation time for epoch 2: 4.16 minutes

Testing unseen_occ...
✅ Hash编码器已设置为eval模式
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 启用
处理查询图像: 100%|████████████████████████████| 4272/4272 [03:40<00:00, 19.37it/s]
 52%|██████████████████████▋                     | 321/621 [31:32<29:28,  5.90s/it]
Epoch-2, unseen_occ -- Mean err: 15.67, Acc: 0.76, Rec : 0.90, Class and Pose  : 0.76
Validation time for epoch 2: 4.26 minutes
✅ Hash编码器已设置为训练模式
 12%|████▊                                   | 3/25 [4:32:36<33:19:08, 5452.19s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
🔍 检查梯度传播:
   Model有梯度: True
   Hash pose编码器有梯度: True
   Hash cls编码器有梯度: True
Traceback (most recent call last):
  File "train_new.py", line 443, in <module>
    main()
  File "train_new.py", line 328, in main
    train_loss = training_utils_dino.train(
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/datasets/linemod/training_utils_dino.py", line 383, in train
    loss_dict = criterion(
  File "/home/<USER>/anaconda3/envs/diff-feats/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1130, in _call_impl
    return forward_call(*input, **kwargs)
  File "/home/<USER>/projects/RJL2025/my-diff-feats-pose/lib/losses/contrast_loss.py", line 156, in forward
    'pos_sim': pose_pos_sim.item(),
KeyboardInterrupt
