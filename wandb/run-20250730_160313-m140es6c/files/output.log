特征提取器可训练参数数量: 3.21M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 3.21M
100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 554/554 [54:10<00:00,  5.87s/it]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 151/151 [01:06<00:00,  2.28it/s]6s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 872/872 [00:56<00:00, 15.37it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 95/95 [00:41<00:00,  2.31it/s]
Epoch-0, seen -- Mean err: 4.15, Acc: 1.00, Rec : 1.00, Class and Pose  : 1.00
Validation time for epoch 0: 2.09 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6061/6061 [06:06<00:00, 16.52it/s]
处理模板数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 113/113 [00:49<00:00,  2.30it/s]
Epoch-0, unseen -- Mean err: 5.25, Acc: 0.98, Rec : 1.00, Class and Pose  : 0.98
Validation time for epoch 0: 6.85 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6649/6649 [06:51<00:00, 16.17it/s]
处理模板数据: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 38/38 [00:16<00:00,  2.26it/s]
Epoch-0, seen_occ -- Mean err: 11.88, Acc: 0.85, Rec : 0.92, Class and Pose  : 0.85
Validation time for epoch 0: 7.71 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1688/1688 [01:17<00:00, 21.80it/s]
  4%|█████                                                                                                                        | 1/25 [1:12:32<29:00:48, 4352.01s/it]
Epoch-0, unseen_occ -- Mean err: 14.46, Acc: 0.78, Rec : 1.00, Class and Pose  : 0.78
Validation time for epoch 0: 1.60 minutes
 49%|██████████████████████████████████████████████████████████████▋                                                                  | 269/554 [26:12<27:17,  5.75s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
