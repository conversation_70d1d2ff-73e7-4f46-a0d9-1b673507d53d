特征提取器可训练参数数量: 2.54M
❌ 偏移量预测功能未启用
❌ Hash编码功能未启用
初始阶段优化器参数数量: 2.54M
100%|████████████████████████████████████████████| 554/554 [53:30<00:00,  5.79s/it]
处理模板数据: 100%|██████████████████████████████| 151/151 [01:04<00:00,  2.33it/s]3s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only

Testing seen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|██████████████████████████████| 872/872 [00:44<00:00, 19.48it/s]
处理模板数据: 100%|████████████████████████████████| 95/95 [00:40<00:00,  2.33it/s]
Epoch-0, seen -- Mean err: 4.30, Acc: 0.99, Rec : 1.00, Class and Pose  : 0.99
Validation time for epoch 0: 1.86 minutes

Testing unseen...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 6061/6061 [04:58<00:00, 20.27it/s]
处理模板数据: 100%|██████████████████████████████| 113/113 [00:48<00:00,  2.33it/s]
Epoch-0, unseen -- Mean err: 6.26, Acc: 0.96, Rec : 1.00, Class and Pose  : 0.96
Validation time for epoch 0: 5.69 minutes

Testing seen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 6649/6649 [05:30<00:00, 20.14it/s]
处理模板数据: 100%|████████████████████████████████| 38/38 [00:16<00:00,  2.26it/s]
Epoch-0, seen_occ -- Mean err: 12.81, Acc: 0.83, Rec : 0.92, Class and Pose  : 0.83
Validation time for epoch 0: 6.35 minutes

Testing unseen_occ...
测试中偏移量预测状态: 未启用
测试中Hash编码器状态: 未启用
处理查询图像: 100%|████████████████████████████| 1688/1688 [01:15<00:00, 22.29it/s]
  4%|█▌                                      | 1/25 [1:09:04<27:37:44, 4144.36s/it]
Epoch-0, unseen_occ -- Mean err: 14.67, Acc: 0.77, Rec : 0.99, Class and Pose  : 0.77
Validation time for epoch 0: 1.58 minutes
 16%|██████▉                                      | 86/554 [08:28<45:15,  5.80s/it]
🔍 Debug: query_features['pose_feature'].shape = torch.Size([16, 128, 16, 16])
🔍 Debug: query_features['cls_feature'].shape = torch.Size([16, 128])
🔍 Debug: 配置中的descriptor_size = 128
🔍 Debug info printed for first batch only
